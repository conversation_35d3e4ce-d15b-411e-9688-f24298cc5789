from fastapi import Fast<PERSON><PERSON>, HTTPException, Body
from MCP_GOOGLE_GEMINI import GoogleGeminiMC<PERSON>

# Replace with your Gemini API key
mcp = GoogleGeminiMCP(api_key="YOUR_GEMINI_API_KEY")

app = FastAPI()

@app.post("/gemini/ask")
def ask_gemini(prompt: str = Body(...)):
    try:
        return mcp.ask(prompt)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
