/**
 * MCP API Gateway - Centralized routing and management for MCP services
 * Provides load balancing, health checks, and service discovery
 */

import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import { createProxyMiddleware, Options } from 'http-proxy-middleware';
import { EventEmitter } from 'events';
import axios from 'axios';

interface MCPService {
  name: string;
  url: string;
  port: number;
  health: 'healthy' | 'unhealthy' | 'unknown';
  lastHealthCheck: Date;
  version?: string;
  capabilities?: string[];
}

interface GatewayConfig {
  port: number;
  healthCheckInterval: number;
  requestTimeout: number;
  retryAttempts: number;
  enableCors: boolean;
  enableRateLimit: boolean;
  rateLimit: {
    windowMs: number;
    max: number;
  };
}

class MCPGateway extends EventEmitter {
  private app: express.Application;
  private services: Map<string, MCPService> = new Map();
  private config: GatewayConfig;
  private healthCheckTimer?: NodeJS.Timeout;

  constructor(config: Partial<GatewayConfig> = {}) {
    super();
    
    this.config = {
      port: config.port || 3000,
      healthCheckInterval: config.healthCheckInterval || 30000,
      requestTimeout: config.requestTimeout || 30000,
      retryAttempts: config.retryAttempts || 3,
      enableCors: config.enableCors !== false,
      enableRateLimit: config.enableRateLimit || true,
      rateLimit: {
        windowMs: config.rateLimit?.windowMs || 15 * 60 * 1000, // 15 minutes
        max: config.rateLimit?.max || 100 // limit each IP to 100 requests per windowMs
      }
    };

    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
    this.loadServices();
  }

  private setupMiddleware() {
    // CORS
    if (this.config.enableCors) {
      this.app.use(cors({
        origin: process.env.ALLOWED_ORIGINS?.split(',') || '*',
        credentials: true
      }));
    }

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Rate limiting
    if (this.config.enableRateLimit) {
      const rateLimit = require('express-rate-limit');
      const limiter = rateLimit(this.config.rateLimit);
      this.app.use('/api/', limiter);
    }

    // Request logging
    this.app.use((req: Request, res: Response, next: NextFunction) => {
      const start = Date.now();
      res.on('finish', () => {
        const duration = Date.now() - start;
        this.emit('request', {
          method: req.method,
          url: req.url,
          status: res.statusCode,
          duration,
          ip: req.ip
        });
      });
      next();
    });

    // Error handling
    this.app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
      console.error('Gateway error:', err);
      this.emit('error', { error: err, request: req });
      res.status(500).json({
        error: 'Internal gateway error',
        message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
      });
    });
  }

  private setupRoutes() {
    // Health check endpoint
    this.app.get('/health', (req: Request, res: Response) => {
      const healthyServices = Array.from(this.services.values())
        .filter(service => service.health === 'healthy').length;
      
      res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        services: {
          total: this.services.size,
          healthy: healthyServices,
          unhealthy: this.services.size - healthyServices
        },
        uptime: process.uptime(),
        memory: process.memoryUsage()
      });
    });

    // Service discovery endpoint
    this.app.get('/api/services', (req: Request, res: Response) => {
      const serviceList = Array.from(this.services.entries()).map(([name, service]) => ({
        name,
        url: service.url,
        health: service.health,
        lastHealthCheck: service.lastHealthCheck,
        capabilities: service.capabilities || []
      }));

      res.json({ services: serviceList });
    });

    // MCP tool execution endpoint
    this.app.post('/api/tools/execute', async (req: Request, res: Response) => {
      try {
        const { tool, service, ...params } = req.body;
        
        if (!tool) {
          return res.status(400).json({ error: 'Tool name is required' });
        }

        // Find appropriate service
        const targetService = service ? this.services.get(service) : this.findServiceForTool(tool);
        
        if (!targetService || targetService.health !== 'healthy') {
          return res.status(503).json({ 
            error: 'Service unavailable',
            service: service || 'auto-detected'
          });
        }

        // Execute tool with retry logic
        const result = await this.executeWithRetry(targetService, tool, params);
        res.json(result);

      } catch (error) {
        console.error('Tool execution error:', error);
        res.status(500).json({
          error: 'Tool execution failed',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    });

    // Dynamic service proxying
    this.app.use('/api/mcp/:serviceName/*', (req: Request, res: Response, next: NextFunction) => {
      const serviceName = req.params.serviceName;
      const service = this.services.get(serviceName);

      if (!service || service.health !== 'healthy') {
        return res.status(503).json({
          error: 'Service unavailable',
          service: serviceName
        });
      }

      const proxyOptions: Options = {
        target: service.url,
        changeOrigin: true,
        pathRewrite: {
          [`^/api/mcp/${serviceName}`]: ''
        },
        timeout: this.config.requestTimeout,
        onError: (err, req, res) => {
          console.error(`Proxy error for ${serviceName}:`, err);
          this.emit('proxyError', { service: serviceName, error: err });
          if (!res.headersSent) {
            res.status(502).json({
              error: 'Bad gateway',
              service: serviceName
            });
          }
        }
      };

      const proxy = createProxyMiddleware(proxyOptions);
      proxy(req, res, next);
    });

    // Catch-all for unknown routes
    this.app.use('*', (req: Request, res: Response) => {
      res.status(404).json({
        error: 'Route not found',
        path: req.originalUrl,
        availableServices: Array.from(this.services.keys())
      });
    });
  }

  private loadServices() {
    // Load services from configuration
    const servicesConfig = require('./mcp_settings.json');
    
    if (servicesConfig.mcpServers) {
      Object.entries(servicesConfig.mcpServers).forEach(([name, url]) => {
        const serviceUrl = url as string;
        const port = parseInt(serviceUrl.split(':').pop() || '3000');
        
        this.services.set(name, {
          name,
          url: serviceUrl,
          port,
          health: 'unknown',
          lastHealthCheck: new Date()
        });
      });
    }

    console.log(`Loaded ${this.services.size} MCP services`);
  }

  private findServiceForTool(tool: string): MCPService | undefined {
    // Simple tool-to-service mapping
    const toolMappings: Record<string, string> = {
      'screenshot': 'mcphub',
      'webSearch': 'mcphub',
      'sendMessage': 'mcphub',
      'createPage': 'mcphub',
      'health': 'mcphub'
    };

    const serviceName = toolMappings[tool] || 'mcphub';
    return this.services.get(serviceName);
  }

  private async executeWithRetry(service: MCPService, tool: string, params: any): Promise<any> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.config.retryAttempts; attempt++) {
      try {
        const response = await axios.post(
          `${service.url}/api/tools/${tool}`,
          params,
          { timeout: this.config.requestTimeout }
        );
        
        return response.data;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        if (attempt < this.config.retryAttempts) {
          // Exponential backoff
          const delay = Math.pow(2, attempt) * 1000;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError || new Error('All retry attempts failed');
  }

  private async performHealthCheck(service: MCPService): Promise<void> {
    try {
      const response = await axios.get(`${service.url}/health`, {
        timeout: 5000
      });
      
      if (response.status === 200) {
        service.health = 'healthy';
        service.lastHealthCheck = new Date();
        
        // Update capabilities if provided
        if (response.data.capabilities) {
          service.capabilities = response.data.capabilities;
        }
      } else {
        service.health = 'unhealthy';
      }
    } catch (error) {
      service.health = 'unhealthy';
      service.lastHealthCheck = new Date();
    }
  }

  private startHealthChecks() {
    this.healthCheckTimer = setInterval(async () => {
      const healthCheckPromises = Array.from(this.services.values())
        .map(service => this.performHealthCheck(service));
      
      await Promise.allSettled(healthCheckPromises);
      
      const healthyCount = Array.from(this.services.values())
        .filter(s => s.health === 'healthy').length;
      
      this.emit('healthCheck', {
        total: this.services.size,
        healthy: healthyCount,
        timestamp: new Date()
      });
    }, this.config.healthCheckInterval);
  }

  async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.app.listen(this.config.port, (err?: Error) => {
        if (err) {
          reject(err);
          return;
        }

        console.log(`🚀 MCP Gateway listening on port ${this.config.port}`);
        console.log(`📊 Managing ${this.services.size} MCP services`);
        
        // Start health checks
        this.startHealthChecks();
        
        this.emit('started', { port: this.config.port });
        resolve();
      });
    });
  }

  async stop(): Promise<void> {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }
    
    this.emit('stopped');
    console.log('MCP Gateway stopped');
  }

  // Public methods for service management
  addService(name: string, service: Omit<MCPService, 'name'>): void {
    this.services.set(name, { ...service, name });
    console.log(`Added MCP service: ${name}`);
  }

  removeService(name: string): boolean {
    const removed = this.services.delete(name);
    if (removed) {
      console.log(`Removed MCP service: ${name}`);
    }
    return removed;
  }

  getService(name: string): MCPService | undefined {
    return this.services.get(name);
  }

  getAllServices(): MCPService[] {
    return Array.from(this.services.values());
  }
}

export { MCPGateway };
export type { MCPService, GatewayConfig };

// Start gateway if this file is run directly
if (require.main === module) {
  const gateway = new MCPGateway();
  
  gateway.on('started', () => {
    console.log('MCP Gateway started successfully');
  });
  
  gateway.on('error', (data) => {
    console.error('Gateway error:', data.error);
  });
  
  gateway.start().catch(console.error);
  
  // Graceful shutdown
  process.on('SIGTERM', () => gateway.stop());
  process.on('SIGINT', () => gateway.stop());
}
