import { MCPServer } from 'model-context-protocol';
import axios from 'axios';

export async function startServer(port = 3029, aiEndpoint: string, apiKey?: string) {
    const server = new MCPServer({ port });
    server.registerTool('askGemini', {
        title: 'Google Gemini AI',
        parameters: {
            type: 'object',
            properties: { prompt: { type: 'string' } },
            required: ['prompt']
        },
        returns: { type: 'object', properties: { response: { type: 'string' } } }
    }, async ({ prompt }) => {
        const resp = await axios.post(aiEndpoint, { prompt, key: apiKey });
        return { response: resp.data.text };
    });
    await server.listen();
}