
/**
 * Web Search MCP Server
 * Provides web search capabilities using various search engines
 */

import { MCPServer, MCPTool, createToolSchema, ToolPropertyTypes } from '../mcp-base';
import axios from 'axios';

interface SearchResult {
  title: string;
  snippet: string;
  url: string;
  rank?: number;
}

class WebSearchMCPServer extends MCPServer {
  private apiKey?: string;
  private iotControllerUrl: string;

  constructor(apiKey?: string) {
    super({
      name: 'Web Search MCP Server',
      version: '1.0.0',
      port: 3006
    });

    this.apiKey = apiKey;
    this.iotControllerUrl = 'http://localhost:3010';
    this.setupTools();
  }

  private setupTools(): void {
    // Web Search Tool
    const webSearchTool: MCPTool = {
      name: 'webSearch',
      description: 'Search the web and return top results',
      inputSchema: createToolSchema({
        query: ToolPropertyTypes.string('Search query'),
        engine: ToolPropertyTypes.string('Search engine (duckduckgo, google, bing)', 'duckduckgo'),
        maxResults: ToolPropertyTypes.number('Maximum number of results', 1, 20),
        safeSearch: ToolPropertyTypes.boolean('Enable safe search', true),
        region: ToolPropertyTypes.string('Search region (us, uk, de, etc.)', 'us')
      }, ['query']),
      handler: async ({ query, engine = 'duckduckgo', maxResults = 10, safeSearch = true, region = 'us' }: {
        query: string;
        engine?: string;
        maxResults?: number;
        safeSearch?: boolean;
        region?: string;
      }) => {
        try {
          if (!query || query.trim().length === 0) {
            return {
              success: false,
              error: 'Search query is required'
            };
          }

          // Mock search results for development
          const mockResults: SearchResult[] = [
            {
              title: `Mock Result 1 for "${query}"`,
              snippet: `This is a mock search result snippet for the query "${query}". In a real implementation, this would be actual search results.`,
              url: `https://example.com/result1?q=${encodeURIComponent(query)}`,
              rank: 1
            },
            {
              title: `Mock Result 2 for "${query}"`,
              snippet: `Another mock search result for "${query}". This demonstrates the structure of search results.`,
              url: `https://example.com/result2?q=${encodeURIComponent(query)}`,
              rank: 2
            },
            {
              title: `Mock Result 3 for "${query}"`,
              snippet: `Third mock result showing how multiple results would be returned for "${query}".`,
              url: `https://example.com/result3?q=${encodeURIComponent(query)}`,
              rank: 3
            }
          ];

          // Limit results to maxResults
          const limitedResults = mockResults.slice(0, Math.min(maxResults, mockResults.length));

          return {
            success: true,
            results: limitedResults,
            metadata: {
              query,
              engine,
              totalResults: limitedResults.length,
              maxResults,
              safeSearch,
              region,
              searchTime: Date.now(),
              isMockData: true
            }
          };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          return {
            success: false,
            error: errorMessage
          };
        }
      }
    };

    // Search Suggestions Tool
    const searchSuggestionsTool: MCPTool = {
      name: 'searchSuggestions',
      description: 'Get search suggestions for a query',
      inputSchema: createToolSchema({
        query: ToolPropertyTypes.string('Partial search query'),
        maxSuggestions: ToolPropertyTypes.number('Maximum number of suggestions', 1, 10)
      }, ['query']),
      handler: async ({ query, maxSuggestions = 5 }: {
        query: string;
        maxSuggestions?: number;
      }) => {
        try {
          if (!query || query.trim().length === 0) {
            return {
              success: false,
              error: 'Query is required for suggestions'
            };
          }

          // Mock suggestions
          const mockSuggestions = [
            `${query} tutorial`,
            `${query} examples`,
            `${query} guide`,
            `${query} documentation`,
            `${query} best practices`,
            `${query} tips`,
            `${query} how to`,
            `${query} vs alternatives`
          ];

          const limitedSuggestions = mockSuggestions.slice(0, Math.min(maxSuggestions, mockSuggestions.length));

          return {
            success: true,
            suggestions: limitedSuggestions,
            metadata: {
              originalQuery: query,
              totalSuggestions: limitedSuggestions.length,
              maxSuggestions,
              isMockData: true
            }
          };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          return {
            success: false,
            error: errorMessage
          };
        }
      }
    };

    // URL Info Tool
    const urlInfoTool: MCPTool = {
      name: 'urlInfo',
      description: 'Get information about a URL',
      inputSchema: createToolSchema({
        url: ToolPropertyTypes.string('URL to analyze')
      }, ['url']),
      handler: async ({ url }: { url: string }) => {
        try {
          if (!url) {
            return {
              success: false,
              error: 'URL is required'
            };
          }

          // Basic URL parsing
          let parsedUrl: URL;
          try {
            parsedUrl = new URL(url);
          } catch (error) {
            return {
              success: false,
              error: 'Invalid URL format'
            };
          }

          return {
            success: true,
            urlInfo: {
              url,
              protocol: parsedUrl.protocol,
              hostname: parsedUrl.hostname,
              pathname: parsedUrl.pathname,
              search: parsedUrl.search,
              hash: parsedUrl.hash,
              port: parsedUrl.port || (parsedUrl.protocol === 'https:' ? '443' : '80'),
              domain: parsedUrl.hostname.replace(/^www\./, ''),
              isSecure: parsedUrl.protocol === 'https:'
            }
          };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          return {
            success: false,
            error: errorMessage
          };
        }
      }
    };

    // IoT-Enhanced Search Tool
    const iotSearchTool: MCPTool = {
      name: 'iotSearch',
      description: 'Search with IoT device context awareness',
      inputSchema: createToolSchema({
        query: ToolPropertyTypes.string('Search query'),
        iotContext: ToolPropertyTypes.object('Current IoT device states and context', {}),
        deviceId: ToolPropertyTypes.string('Specific device ID for context'),
        maxResults: ToolPropertyTypes.number('Maximum number of results', 1, 20)
      }, ['query']),
      handler: async (params: any) => {
        return await this.iotAwareSearch(params.query, params.iotContext, params.deviceId, params.maxResults);
      }
    };

    // Process Voice Command Tool
    const processVoiceCommandTool: MCPTool = {
      name: 'processVoiceCommand',
      description: 'Process voice commands for search with IoT context',
      inputSchema: createToolSchema({
        command: ToolPropertyTypes.string('Natural language search command'),
        speaker: ToolPropertyTypes.string('Speaker/user identifier'),
        iotContext: ToolPropertyTypes.object('Current IoT device states', {})
      }, ['command']),
      handler: async (params: any) => {
        return await this.processVoiceSearchCommand(params.command, params.speaker, params.iotContext);
      }
    };

    // Add all tools
    this.addTool(webSearchTool);
    this.addTool(searchSuggestionsTool);
    this.addTool(urlInfoTool);
    this.addTool(iotSearchTool);
    this.addTool(processVoiceCommandTool);
  }

  private async iotAwareSearch(query: string, iotContext?: any, deviceId?: string, maxResults: number = 5): Promise<any> {
    try {
      // Enhance query with IoT context
      let enhancedQuery = query;
      if (iotContext || deviceId) {
        enhancedQuery = this.enhanceQueryWithIoTContext(query, iotContext, deviceId);
      }

      // Get device-specific context if deviceId provided
      let deviceContext = null;
      if (deviceId) {
        try {
          const response = await axios.get(`${this.iotControllerUrl}/devices/${deviceId}`);
          if (response.status === 200) {
            deviceContext = response.data;
          }
        } catch (error) {
          console.warn(`Failed to get device context for ${deviceId}:`, error);
        }
      }

      // Create IoT-enhanced search results
      const iotResults = this.generateIoTSearchResults(enhancedQuery, iotContext, deviceContext);

      // Add standard search results
      const standardResults = [
        {
          title: `IoT-Enhanced Search: ${enhancedQuery}`,
          snippet: `Smart home aware search results for "${enhancedQuery}". Context: ${JSON.stringify(iotContext || {})}`,
          url: `https://smarthome-search.example.com/q=${encodeURIComponent(enhancedQuery)}`,
          rank: 1
        }
      ];

      const allResults = [...iotResults, ...standardResults].slice(0, maxResults);

      return {
        success: true,
        results: allResults,
        metadata: {
          query: enhancedQuery,
          originalQuery: query,
          iotContext,
          deviceId,
          deviceContext,
          totalResults: allResults.length,
          isIoTEnhanced: true
        }
      };
    } catch (error) {
      return {
        success: false,
        error: `IoT search failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  private enhanceQueryWithIoTContext(query: string, iotContext?: any, deviceId?: string): string {
    let enhancedQuery = query;

    // Add device-specific context
    if (deviceId) {
      enhancedQuery += ` ${deviceId}`;
    }

    // Add device states context
    if (iotContext?.deviceStates) {
      const activeDevices = Object.keys(iotContext.deviceStates)
        .filter(id => iotContext.deviceStates[id].online);
      if (activeDevices.length > 0) {
        enhancedQuery += ` smart home ${activeDevices.join(' ')}`;
      }
    }

    // Add IoT keywords if query seems IoT-related
    if (this.isIoTRelatedQuery(query)) {
      enhancedQuery += ' automation smart home IoT';
    }

    return enhancedQuery;
  }

  private isIoTRelatedQuery(query: string): boolean {
    const iotKeywords = [
      'smart home', 'iot', 'automation', 'sensor', 'device', 'control',
      'lights', 'thermostat', 'camera', 'security', 'alexa', 'google home',
      'zigbee', 'zwave', 'wifi', 'bluetooth', 'matter', 'homekit', 'hub'
    ];

    const lowerQuery = query.toLowerCase();
    return iotKeywords.some(keyword => lowerQuery.includes(keyword));
  }

  private generateIoTSearchResults(query: string, iotContext?: any, deviceContext?: any): any[] {
    const results: any[] = [];

    // Use iotContext parameter to avoid unused warning
    if (iotContext) {
      console.debug('IoT context available for search enhancement');
    }

    // Add device-specific results
    if (deviceContext) {
      results.push({
        title: `${deviceContext.name} - Device Information`,
        snippet: `Information and troubleshooting for your ${deviceContext.type} device. Status: ${deviceContext.state}`,
        url: `https://device-help.example.com/${deviceContext.id}`,
        rank: 1
      });
    }

    // Add IoT-specific results if query is IoT-related
    if (this.isIoTRelatedQuery(query)) {
      results.push(
        {
          title: 'Smart Home Device Compatibility Guide',
          snippet: 'Find compatible IoT devices for your smart home setup. Supports Zigbee, Z-Wave, WiFi, and Matter protocols.',
          url: 'https://smarthome.example.com/compatibility',
          rank: 2
        },
        {
          title: 'IoT Device Troubleshooting',
          snippet: 'Common solutions for IoT device connectivity and automation issues.',
          url: 'https://support.example.com/iot-troubleshooting',
          rank: 3
        },
        {
          title: 'Home Assistant Integration Guide',
          snippet: 'Step-by-step guide to integrate your IoT devices with Home Assistant.',
          url: 'https://homeassistant.example.com/integrations',
          rank: 4
        }
      );
    }

    return results;
  }

  private async processVoiceSearchCommand(command: string, speaker?: string, iotContext?: any): Promise<any> {
    const lowerCommand = command.toLowerCase();

    // Use speaker parameter to avoid unused warning
    if (speaker) {
      console.debug(`Processing search command from speaker: ${speaker}`);
    }

    // Voice search patterns with IoT context
    const patterns = [
      {
        pattern: /search for (.+) (?:for|about) (.+) device/,
        action: (matches: RegExpMatchArray) => ({
          query: matches[1],
          deviceId: matches[2].replace(/\s+/g, '_')
        })
      },
      {
        pattern: /find (.+) (?:smart home|iot|automation)/,
        action: (matches: RegExpMatchArray) => ({
          query: `${matches[1]} smart home automation`
        })
      },
      {
        pattern: /search (.+)/,
        action: (matches: RegExpMatchArray) => ({
          query: matches[1]
        })
      }
    ];

    for (const pattern of patterns) {
      const matches = lowerCommand.match(pattern.pattern);
      if (matches) {
        const parsed = pattern.action(matches);
        return await this.iotAwareSearch(parsed.query, iotContext, (parsed as any).deviceId);
      }
    }

    return {
      success: false,
      error: 'Could not understand search command',
      command,
      suggestion: 'Try commands like "search for smart bulb troubleshooting" or "find automation guides"'
    };
  }

  protected override getMetrics(): any {
    const baseMetrics = super.getMetrics();
    return {
      ...baseMetrics,
      supportedEngines: ['duckduckgo', 'google', 'bing'],
      hasApiKey: !!this.apiKey,
      maxResults: 20,
      iotIntegration: true,
      iotControllerUrl: this.iotControllerUrl
    };
  }
}

// Export for use in other modules
export async function startServer(port = 3006, apiKey?: string): Promise<WebSearchMCPServer> {
  const server = new WebSearchMCPServer(apiKey);
  await server.start(port);
  return server;
}

// Start server if this file is run directly
if (require.main === module) {
  const apiKey = process.env.SEARCH_API_KEY;
  startServer(3006, apiKey).catch(console.error);
}
