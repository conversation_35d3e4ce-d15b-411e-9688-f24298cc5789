from fastapi import FastAPI, HTTPException, Body
from MCP_LANGGRAPH_DISPATCH import LangGraphDispatchMCP

mcp = LangGraphDispatchMCP()
app = FastAPI()

@app.post("/langgraph/dispatch")
def dispatch_task(instruction: str = Body(...)):
    try:
        return mcp.dispatch_task(instruction)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/langgraph/routes")
def get_routes():
    try:
        return mcp.all_routes()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
