from datetime import datetime, timedelta
import uuid

class CalendarManagerMCP:
    def __init__(self):
        self.events = {}

    def create_event(self, title: str, start_time: str, duration_minutes: int = 60):
        try:
            start = datetime.fromisoformat(start_time)
            end = start + timedelta(minutes=duration_minutes)
            event_id = str(uuid.uuid4())
            self.events[event_id] = {
                "id": event_id,
                "title": title,
                "start": start.isoformat(),
                "end": end.isoformat()
            }
            return self.events[event_id]
        except Exception as e:
            return {"error": str(e)}

    def list_events(self):
        return list(self.events.values())

    def delete_event(self, event_id: str):
        if event_id in self.events:
            del self.events[event_id]
            return {"status": "deleted", "id": event_id}
        return {"error": "Event not found"}
