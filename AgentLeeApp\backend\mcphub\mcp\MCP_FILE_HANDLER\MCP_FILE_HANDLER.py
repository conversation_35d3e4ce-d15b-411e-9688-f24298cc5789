import os
import shutil

class FileHandlerMCP:
    def read_file(self, path: str):
        try:
            with open(path, "r", encoding="utf-8") as f:
                return {"content": f.read()}
        except Exception as e:
            return {"error": str(e)}

    def write_file(self, path: str, content: str):
        try:
            with open(path, "w", encoding="utf-8") as f:
                f.write(content)
            return {"status": "written", "path": path}
        except Exception as e:
            return {"error": str(e)}

    def delete_file(self, path: str):
        try:
            os.remove(path)
            return {"status": "deleted", "path": path}
        except Exception as e:
            return {"error": str(e)}

    def copy_file(self, src: str, dst: str):
        try:
            shutil.copy2(src, dst)
            return {"status": "copied", "src": src, "dst": dst}
        except Exception as e:
            return {"error": str(e)}
