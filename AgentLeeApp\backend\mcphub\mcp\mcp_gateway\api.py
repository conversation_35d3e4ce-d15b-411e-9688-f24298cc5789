from fastapi import FastAPI, HTTPException, Body, Query
from mcp_gateway import MCPGateway

mcp = MCPGateway()
app = FastAPI()

@app.post("/gateway/register")
def register_route(name: str = Query(...), url: str = Query(...)):
    try:
        return mcp.register_route(name, url)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/gateway/forward")
def forward(route: str = Query(...), payload: dict = Body(...)):
    try:
        return mcp.forward_request(route, payload)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/gateway/routes")
def list_all_routes():
    try:
        return mcp.list_routes()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
