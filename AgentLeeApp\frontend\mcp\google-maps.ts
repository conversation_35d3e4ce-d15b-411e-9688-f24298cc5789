import { MCPServer } from 'model-context-protocol';
import axios from 'axios';

export async function startServer(port = 3016, apiKey: string) {
    const server = new MCPServer({ port });
    server.registerTool('geocode', {
        title: 'Google Maps Geocode',
        parameters: { type: 'object', properties: { address: { type: 'string' } }, required: ['address'] },
        returns: { type: 'object', properties: { lat: { type: 'number' }, lng: { type: 'number' } } }
    }, async ({ address }) => {
        const resp = await axios.get('https://maps.googleapis.com/maps/api/geocode/json', {
            params: { address, key: apiKey }
        });
        const loc = resp.data.results?.[0]?.geometry?.location;
        if (!loc) throw new Error('Not found');
        return { lat: loc.lat, lng: loc.lng };
    });
    await server.listen();
}