import cv2

class VideoStreamerMCP:
    def __init__(self, camera_index=0):
        self.camera_index = camera_index
        self.capture = None

    def start_stream(self):
        self.capture = cv2.VideoCapture(self.camera_index)
        if not self.capture.isOpened():
            return {"error": "Could not open camera."}
        return {"status": "stream started"}

    def read_frame(self):
        if self.capture is None or not self.capture.isOpened():
            return {"error": "Stream not started."}
        ret, frame = self.capture.read()
        if not ret:
            return {"error": "Failed to read frame."}
        _, buffer = cv2.imencode(".jpg", frame)
        return buffer.tobytes()

    def stop_stream(self):
        if self.capture:
            self.capture.release()
        return {"status": "stream stopped"}
