/**
 * Phone Control MCP Server with IoT Integration
 * Enables phone automation triggered by IoT events and voice commands
 */

import { MCPServer, MCPTool, MCPResource, createToolSchema, ToolPropertyTypes } from '../mcp-base';
import axios from 'axios';

interface PhoneAction {
  type: 'call' | 'sms' | 'notification' | 'app_launch' | 'screenshot';
  target?: string;
  message?: string;
  appPackage?: string;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
}

interface IoTTrigger {
  deviceId: string;
  condition: string;
  value?: any;
  action: PhoneAction;
}

class PhoneControlMCPServer extends MCPServer {
  private iotControllerUrl: string;
  private phoneServiceUrl: string;
  private activeTriggers: Map<string, IoTTrigger> = new Map();

  constructor() {
    super({
      name: 'Phone Control MCP Server',
      version: '1.0.0',
      port: 3014
    });

    this.iotControllerUrl = 'http://localhost:3010';
    this.phoneServiceUrl = 'http://phone:8010';
    this.setupTools();
    this.setupResources();
  }

  private setupTools(): void {
    // Make Phone Call Tool
    const makeCallTool: MCPTool = {
      name: 'makeCall',
      description: 'Make a phone call to a specific number',
      inputSchema: createToolSchema({
        number: ToolPropertyTypes.string('Phone number to call'),
        speaker: ToolPropertyTypes.string('Speaker/user identifier')
      }, ['number']),
      handler: async (params: any) => {
        return await this.makeCall(params.number, params.speaker);
      }
    };

    // Send SMS Tool
    const sendSMSTool: MCPTool = {
      name: 'sendSMS',
      description: 'Send SMS message to a phone number',
      inputSchema: createToolSchema({
        number: ToolPropertyTypes.string('Phone number to send SMS'),
        message: ToolPropertyTypes.string('SMS message content'),
        speaker: ToolPropertyTypes.string('Speaker/user identifier')
      }, ['number', 'message']),
      handler: async (params: any) => {
        return await this.sendSMS(params.number, params.message, params.speaker);
      }
    };

    // IoT-Triggered Phone Action Tool
    const iotPhoneActionTool: MCPTool = {
      name: 'iotPhoneAction',
      description: 'Execute phone action based on IoT device trigger',
      inputSchema: createToolSchema({
        deviceId: ToolPropertyTypes.string('IoT device ID that triggered the action'),
        action: ToolPropertyTypes.string('Phone action to execute (call, sms, notification)'),
        target: ToolPropertyTypes.string('Phone number or target for the action'),
        message: ToolPropertyTypes.string('Message content for SMS or notification'),
        priority: ToolPropertyTypes.string('Action priority (low, normal, high, urgent)', 'normal')
      }, ['deviceId', 'action']),
      handler: async (params: any) => {
        return await this.executeIoTPhoneAction(params);
      }
    };

    // Setup IoT Trigger Tool
    const setupIoTTriggerTool: MCPTool = {
      name: 'setupIoTTrigger',
      description: 'Setup phone action to be triggered by IoT device events',
      inputSchema: createToolSchema({
        deviceId: ToolPropertyTypes.string('IoT device ID to monitor'),
        condition: ToolPropertyTypes.string('Trigger condition (motion_detected, door_opened, temperature_high, etc.)'),
        phoneAction: ToolPropertyTypes.string('Phone action to execute (call, sms, notification)'),
        target: ToolPropertyTypes.string('Phone number or target'),
        message: ToolPropertyTypes.string('Message content'),
        speaker: ToolPropertyTypes.string('Speaker/user identifier')
      }, ['deviceId', 'condition', 'phoneAction']),
      handler: async (params: any) => {
        return await this.setupIoTTrigger(params);
      }
    };

    // Process Voice Command Tool
    const processVoiceCommandTool: MCPTool = {
      name: 'processVoiceCommand',
      description: 'Process natural language voice commands for phone control with IoT context',
      inputSchema: createToolSchema({
        command: ToolPropertyTypes.string('Natural language command'),
        speaker: ToolPropertyTypes.string('Speaker/user identifier'),
        iotContext: ToolPropertyTypes.object('Current IoT device states and context', {})
      }, ['command']),
      handler: async (params: any) => {
        return await this.processVoiceCommand(params.command, params.speaker, params.iotContext);
      }
    };

    // Register all tools
    this.addTool(makeCallTool);
    this.addTool(sendSMSTool);
    this.addTool(iotPhoneActionTool);
    this.addTool(setupIoTTriggerTool);
    this.addTool(processVoiceCommandTool);
  }

  private setupResources(): void {
    // Active Triggers Resource
    const triggersResource: MCPResource = {
      uri: 'phone://triggers',
      name: 'Active IoT Phone Triggers',
      description: 'List of active IoT triggers that can execute phone actions',
      mimeType: 'application/json'
    };

    // Phone Status Resource
    const phoneStatusResource: MCPResource = {
      uri: 'phone://status',
      name: 'Phone Service Status',
      description: 'Current status of phone service and recent actions',
      mimeType: 'application/json'
    };

    // Call History Resource
    const callHistoryResource: MCPResource = {
      uri: 'phone://call-history',
      name: 'Call History',
      description: 'Recent phone calls made through the system',
      mimeType: 'application/json'
    };

    this.addResource(triggersResource);
    this.addResource(phoneStatusResource);
    this.addResource(callHistoryResource);
  }

  private async makeCall(number: string, speaker?: string): Promise<any> {
    try {
      const response = await axios.post(`${this.phoneServiceUrl}/call`, {
        number,
        speaker,
        timestamp: new Date().toISOString()
      });

      return {
        success: true,
        action: 'call_initiated',
        number,
        speaker,
        timestamp: new Date().toISOString(),
        response: response.data
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to make call: ${error instanceof Error ? error.message : 'Unknown error'}`,
        number,
        speaker
      };
    }
  }

  private async sendSMS(number: string, message: string, speaker?: string): Promise<any> {
    try {
      const response = await axios.post(`${this.phoneServiceUrl}/sms`, {
        number,
        message,
        speaker,
        timestamp: new Date().toISOString()
      });

      return {
        success: true,
        action: 'sms_sent',
        number,
        message,
        speaker,
        timestamp: new Date().toISOString(),
        response: response.data
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to send SMS: ${error instanceof Error ? error.message : 'Unknown error'}`,
        number,
        message,
        speaker
      };
    }
  }

  private async executeIoTPhoneAction(params: any): Promise<any> {
    const { deviceId, action, target, message, priority = 'normal' } = params;

    try {
      // Get device status from IoT controller
      const deviceResponse = await axios.get(`${this.iotControllerUrl}/devices/${deviceId}`);
      const device = deviceResponse.data;

      let result: any;
      switch (action) {
        case 'call':
          if (!target) throw new Error('Phone number required for call action');
          result = await this.makeCall(target, `IoT-${deviceId}`);
          break;

        case 'sms':
          if (!target || !message) throw new Error('Phone number and message required for SMS action');
          result = await this.sendSMS(target, message, `IoT-${deviceId}`);
          break;

        case 'notification':
          result = await this.sendNotification(message || `Alert from ${device.name}`, priority);
          break;

        default:
          throw new Error(`Unsupported phone action: ${action}`);
      }

      return {
        success: true,
        deviceId,
        deviceName: device.name,
        action,
        priority,
        result,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        success: false,
        error: `IoT phone action failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        deviceId,
        action
      };
    }
  }

  private async setupIoTTrigger(params: any): Promise<any> {
    const { deviceId, condition, phoneAction, target, message, speaker } = params;

    const trigger: IoTTrigger = {
      deviceId,
      condition,
      action: {
        type: phoneAction as any,
        target,
        message,
        priority: 'normal'
      }
    };

    const triggerId = `${deviceId}-${condition}-${phoneAction}`;
    this.activeTriggers.set(triggerId, trigger);

    // Register trigger with IoT controller
    try {
      await axios.post(`${this.iotControllerUrl}/triggers`, {
        id: triggerId,
        deviceId,
        condition,
        callback: `${this.config.name}/iotPhoneAction`,
        params: {
          deviceId,
          action: phoneAction,
          target,
          message
        }
      });

      return {
        success: true,
        triggerId,
        trigger,
        message: `IoT phone trigger setup successfully`,
        speaker
      };
    } catch (error) {
      this.activeTriggers.delete(triggerId);
      return {
        success: false,
        error: `Failed to setup IoT trigger: ${error instanceof Error ? error.message : 'Unknown error'}`,
        triggerId
      };
    }
  }

  private async sendNotification(message: string, priority: string): Promise<any> {
    try {
      const response = await axios.post(`${this.phoneServiceUrl}/notification`, {
        message,
        priority,
        timestamp: new Date().toISOString()
      });

      return {
        success: true,
        action: 'notification_sent',
        message,
        priority,
        response: response.data
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to send notification: ${error instanceof Error ? error.message : 'Unknown error'}`,
        message
      };
    }
  }

  private async processVoiceCommand(command: string, speaker?: string, iotContext?: any): Promise<any> {
    const lowerCommand = command.toLowerCase();

    // Phone call patterns with IoT context
    const callPatterns = [
      {
        pattern: /call (.+) when (.+) (is|are) (.+)/,
        action: (matches: RegExpMatchArray) => ({
          action: 'setup_trigger',
          target: matches[1],
          condition: `${matches[2]}_${matches[4]}`,
          phoneAction: 'call'
        })
      },
      {
        pattern: /call (.+)/,
        action: (matches: RegExpMatchArray) => ({
          action: 'call',
          target: matches[1]
        })
      }
    ];

    // SMS patterns with IoT context
    const smsPatterns = [
      {
        pattern: /send (?:sms|text) to (.+) saying (.+) when (.+) (is|are) (.+)/,
        action: (matches: RegExpMatchArray) => ({
          action: 'setup_trigger',
          target: matches[1],
          message: matches[2],
          condition: `${matches[3]}_${matches[5]}`,
          phoneAction: 'sms'
        })
      },
      {
        pattern: /send (?:sms|text) to (.+) saying (.+)/,
        action: (matches: RegExpMatchArray) => ({
          action: 'sms',
          target: matches[1],
          message: matches[2]
        })
      }
    ];

    // Check all patterns
    const allPatterns = [...callPatterns, ...smsPatterns];

    for (const pattern of allPatterns) {
      const matches = lowerCommand.match(pattern.pattern);
      if (matches) {
        const parsed = pattern.action(matches);

        if (parsed.action === 'setup_trigger') {
          return await this.setupIoTTrigger({
            deviceId: 'auto-detected',
            condition: (parsed as any).condition,
            phoneAction: (parsed as any).phoneAction,
            target: parsed.target,
            message: (parsed as any).message,
            speaker
          });
        } else if (parsed.action === 'call') {
          return await this.makeCall(parsed.target, speaker);
        } else if (parsed.action === 'sms') {
          return await this.sendSMS(parsed.target, (parsed as any).message, speaker);
        }
      }
    }

    return {
      success: false,
      error: 'Could not understand phone command',
      command,
      suggestion: 'Try commands like "call John" or "send SMS to mom saying hello"'
    };
  }

  protected override async getResourceContent(resource: MCPResource): Promise<any> {
    switch (resource.uri) {
      case 'phone://triggers':
        return {
          triggers: Array.from(this.activeTriggers.entries()).map(([id, trigger]) => ({
            id,
            ...trigger
          })),
          count: this.activeTriggers.size
        };

      case 'phone://status':
        return {
          service: 'Phone Control MCP',
          status: 'active',
          phoneServiceUrl: this.phoneServiceUrl,
          iotControllerUrl: this.iotControllerUrl,
          activeTriggers: this.activeTriggers.size,
          timestamp: new Date().toISOString()
        };

      case 'phone://call-history':
        // This would typically come from a database or log file
        return {
          recent_calls: [],
          message: 'Call history not implemented yet'
        };

      default:
        return { error: 'Resource not found' };
    }
  }
}

// Export for use as module
export default PhoneControlMCPServer;

// CLI support
if (require.main === module) {
  const server = new PhoneControlMCPServer();
  server.start().then(() => {
    console.log('📱 Phone Control MCP Server with IoT integration started');
  }).catch(console.error);
}
