import discord
import asyncio

class DiscordMCP:
    def __init__(self, token):
        self.token = token
        self.client = discord.Client(intents=discord.Intents.default())

        @self.client.event
        async def on_ready():
            print(f'✅ Logged in as {self.client.user}')

        @self.client.event
        async def on_message(message):
            if message.author == self.client.user:
                return
            if message.content.startswith("!hello"):
                await message.channel.send("👋 Hello from <PERSON>'s MCP!")

    def run_bot(self):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(self.client.start(self.token))
