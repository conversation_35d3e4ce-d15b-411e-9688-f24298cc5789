from pptx import Presentation
from pptx.util import Inches
import os

class AISlideBuilderMCP:
    def __init__(self, output_dir="slides"):
        os.makedirs(output_dir, exist_ok=True)
        self.output_dir = output_dir

    def create_slide_deck(self, title: str, bullets: list, filename="presentation.pptx"):
        prs = Presentation()
        slide_layout = prs.slide_layouts[0]  # Title slide
        slide = prs.slides.add_slide(slide_layout)
        slide.shapes.title.text = title
        slide.placeholders[1].text = "Generated by <PERSON>"

        # Add a bullet slide
        bullet_slide_layout = prs.slide_layouts[1]
        bullet_slide = prs.slides.add_slide(bullet_slide_layout)
        bullet_slide.shapes.title.text = "Key Points"
        body_shape = bullet_slide.placeholders[1]
        tf = body_shape.text_frame
        for b in bullets:
            tf.add_paragraph().text = b

        save_path = os.path.join(self.output_dir, filename)
        prs.save(save_path)
        return {"status": "created", "path": save_path}
