from fastapi import FastAP<PERSON>, HTTPException, Body
from git import GitMCP

app = FastAPI()
mcp = GitMCP()

@app.get("/git/status")
def git_status():
    try:
        return mcp.get_status()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/git/pull")
def git_pull():
    try:
        return mcp.pull()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/git/push")
def git_push():
    try:
        return mcp.push()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/git/commit-push")
def commit_push(commit_msg: str = Body(...)):
    try:
        return mcp.add_commit_push(commit_msg)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
