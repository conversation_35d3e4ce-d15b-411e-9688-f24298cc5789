
/**
 * Telegram Bot MCP Server
 * Provides Telegram Bot API integration for sending messages and managing chats
 */

import { MCPServer, MCPTool, createToolSchema, ToolPropertyTypes } from '../mcp-base';

// Mock Telegram Bot for development (replace with real node-telegram-bot-api when available)
interface TelegramBotAPI {
  sendMessage: (chatId: number, text: string, options?: any) => Promise<any>;
  getMe: () => Promise<any>;
  getChat: (chatId: number) => Promise<any>;
}

class MockTelegramBot implements TelegramBotAPI {
  async sendMessage(chatId: number, text: string, options?: any) {
    return {
      message_id: Date.now(),
      chat: { id: chatId, type: 'private' },
      text,
      date: Math.floor(Date.now() / 1000)
    };
  }

  async getMe() {
    return {
      id: 123456789,
      is_bot: true,
      first_name: '<PERSON><PERSON> Bot',
      username: 'mockbot'
    };
  }

  async getChat(chatId: number) {
    return {
      id: chatId,
      type: 'private',
      first_name: 'Mock User'
    };
  }
}

class TelegramMCPServer extends MCPServer {
  private bot: TelegramBotAPI;

  constructor(telegramToken?: string) {
    super({
      name: 'Telegram Bot MCP Server',
      version: '1.0.0',
      port: 3003
    });

    // Use mock bot if no token provided (for development)
    if (telegramToken && telegramToken !== 'mock') {
      try {
        // Try to import real Telegram bot
        const TelegramBot = require('node-telegram-bot-api');
        this.bot = new TelegramBot(telegramToken, { polling: false });
      } catch (error) {
        console.warn('Telegram bot API not available, using mock bot');
        this.bot = new MockTelegramBot();
      }
    } else {
      this.bot = new MockTelegramBot();
    }

    this.setupTools();
  }

  private setupTools(): void {
    // Send Message Tool
    const sendMessageTool: MCPTool = {
      name: 'sendMessage',
      description: 'Send a text message via Telegram Bot API',
      inputSchema: createToolSchema({
        chatId: ToolPropertyTypes.number('Chat ID to send message to'),
        text: ToolPropertyTypes.string('Message text to send'),
        parseMode: ToolPropertyTypes.string('Parse mode (HTML, Markdown, MarkdownV2)', ''),
        disablePreview: ToolPropertyTypes.boolean('Disable web page preview', false)
      }, ['chatId', 'text']),
      handler: async ({ chatId, text, parseMode, disablePreview }: {
        chatId: number;
        text: string;
        parseMode?: string;
        disablePreview?: boolean;
      }) => {
        try {
          const options: any = {};
          if (parseMode) options.parse_mode = parseMode;
          if (disablePreview) options.disable_web_page_preview = true;

          const msg = await this.bot.sendMessage(chatId, text, options);

          return {
            success: true,
            messageId: msg.message_id,
            chatId: msg.chat.id,
            text: msg.text,
            date: msg.date
          };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          return {
            success: false,
            error: errorMessage
          };
        }
      }
    };

    // Get Bot Info Tool
    const getBotInfoTool: MCPTool = {
      name: 'getBotInfo',
      description: 'Get information about the bot',
      inputSchema: createToolSchema({}),
      handler: async () => {
        try {
          const botInfo = await this.bot.getMe();

          return {
            success: true,
            botInfo
          };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          return {
            success: false,
            error: errorMessage
          };
        }
      }
    };

    // Add all tools
    this.addTool(sendMessageTool);
    this.addTool(getBotInfoTool);
  }

  protected override getMetrics(): any {
    const baseMetrics = super.getMetrics();
    return {
      ...baseMetrics,
      botType: this.bot instanceof MockTelegramBot ? 'mock' : 'real'
    };
  }
}

// Export for use in other modules
export async function startServer(port = 3003, telegramToken?: string): Promise<TelegramMCPServer> {
  const server = new TelegramMCPServer(telegramToken);
  await server.start(port);
  return server;
}

// Start server if this file is run directly
if (require.main === module) {
  const telegramToken = process.env.TELEGRAM_BOT_TOKEN || 'mock';
  startServer(3003, telegramToken).catch(console.error);
}
