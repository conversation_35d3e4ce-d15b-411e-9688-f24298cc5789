import { MCPServer } from 'model-context-protocol';
import textToSpeech from '@google-cloud/text-to-speech';

export async function startServer(port = 3030, credentials: any) {
    const client = new textToSpeech.TextToSpeechClient(credentials);
    const server = new MCPServer({ port });
    server.registerTool('googleTTS', {
        title: 'Google TTS',
        parameters: {
            type: 'object',
            properties: { text: { type: 'string' }, voice: { type: 'string' } },
            required: ['text']
        },
        returns: { type: 'object', properties: { audio_base64: { type: 'string' } } }
    }, async ({ text, voice }) => {
        const [resp] = await client.synthesizeSpeech({
            input: { text },
            voice: { languageCode: voice || 'en-US', ssmlGender: 'NEUTRAL' },
            audioConfig: { audioEncoding: 'MP3' }
        });
        const base64 = Buffer.from(resp.audioContent as Buffer).toString('base64');
        return { audio_base64: base64 };
    });
    await server.listen();
}