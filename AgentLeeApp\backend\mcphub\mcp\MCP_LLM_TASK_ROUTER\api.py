from fastapi import FastAPI, HTTPException, Query
from MCP_LLM_TASK_ROUTER import LLMTaskRouterMCP

mcp = LLMTaskRouterMCP()
app = FastAPI()

@app.get("/llm/route")
def route_task(task_type: str = Query(...)):
    try:
        return mcp.route_task(task_type)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/llm/routes")
def get_routes():
    try:
        return mcp.list_routes()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
