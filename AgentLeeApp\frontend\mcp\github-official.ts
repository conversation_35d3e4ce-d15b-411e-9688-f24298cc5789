import { MCPServer } from 'model-context-protocol';
import { Octokit } from 'octokit';

export async function startServer(port = 3015, githubToken: string) {
    const octo = new Octokit({ auth: githubToken });
    const user = await octo.rest.users.getAuthenticated();
    const server = new MCPServer({ port });
    server.registerTool('listRepos', {
        title: 'GitHub List Repos',
        parameters: { type: 'object', properties: { visibility: { type: 'string', default: 'all' } } },
        returns: { type: 'object', properties: { repos: { type: 'array', items: { type: 'object', properties: { name: { type: 'string' }, url: { type: 'string' } } } } } }
    }, async ({ visibility }) => {
        const resp = await octo.rest.repos.listForAuthenticatedUser({ visibility });
        return { repos: resp.data.map(r => ({ name: r.name, url: r.html_url })) };
    });
    await server.listen();
}