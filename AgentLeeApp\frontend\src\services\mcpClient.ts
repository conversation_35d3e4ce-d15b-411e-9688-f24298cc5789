/**
 * MCP Client SDK - Frontend interface for MCP services
 * Provides type-safe access to all MCP tools and services
 */

export interface MCPService {
  name: string;
  url: string;
  health: 'healthy' | 'unhealthy' | 'unknown';
  capabilities: string[];
}

export interface MCPResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  metadata?: {
    service: string;
    timestamp: string;
    duration?: number;
  };
}

export interface ScreenshotParams {
  url: string;
  width?: number;
  height?: number;
  fullPage?: boolean;
  waitForSelector?: string;
  delay?: number;
}

export interface ScreenshotResult {
  image_base64: string;
  metadata: {
    url: string;
    timestamp: string;
    dimensions: { width: number; height: number; fullPage: boolean };
    success: boolean;
    processingTime?: number;
  };
}

export interface WebSearchParams {
  query: string;
  engine?: 'duckduckgo' | 'google';
  limit?: number;
}

export interface WebSearchResult {
  results: Array<{
    title: string;
    snippet: string;
    url: string;
  }>;
}

export interface TelegramParams {
  chatId: number;
  text: string;
}

export interface NotionParams {
  parentId: string;
  title: string;
  content?: string;
}

class MCPClient {
  private baseUrl: string;
  private apiKey?: string;
  private timeout: number;
  private retryAttempts: number;

  constructor(options: {
    baseUrl?: string;
    apiKey?: string;
    timeout?: number;
    retryAttempts?: number;
  } = {}) {
    this.baseUrl = options.baseUrl || this.detectBackendUrl();
    this.apiKey = options.apiKey;
    this.timeout = options.timeout || 30000;
    this.retryAttempts = options.retryAttempts || 3;
  }

  private detectBackendUrl(): string {
    const hostname = window.location.hostname;
    if (hostname === 'localhost' || hostname.includes('127.0.0.1')) {
      return 'http://localhost:3000'; // MCP Gateway port
    }
    return 'https://agentlee.fly.dev';
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<MCPResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.apiKey) {
      headers['Authorization'] = `Bearer ${this.apiKey}`;
    }

    const requestOptions: RequestInit = {
      ...options,
      headers,
      signal: AbortSignal.timeout(this.timeout),
    };

    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      try {
        const response = await fetch(url, requestOptions);
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        
        return {
          success: true,
          data,
          metadata: {
            service: 'mcp-gateway',
            timestamp: new Date().toISOString(),
          },
        };
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        if (attempt < this.retryAttempts) {
          // Exponential backoff
          const delay = Math.pow(2, attempt) * 1000;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    return {
      success: false,
      error: lastError?.message || 'Request failed after all retries',
      metadata: {
        service: 'mcp-gateway',
        timestamp: new Date().toISOString(),
      },
    };
  }

  // Service discovery and health
  async getServices(): Promise<MCPResponse<{ services: MCPService[] }>> {
    return this.makeRequest('/api/services');
  }

  async getHealth(): Promise<MCPResponse<any>> {
    return this.makeRequest('/health');
  }

  // Screenshot service
  async takeScreenshot(params: ScreenshotParams): Promise<MCPResponse<ScreenshotResult>> {
    return this.makeRequest('/api/tools/execute', {
      method: 'POST',
      body: JSON.stringify({
        tool: 'screenshot',
        service: 'mcphub',
        ...params,
      }),
    });
  }

  // Web search service
  async searchWeb(params: WebSearchParams): Promise<MCPResponse<WebSearchResult>> {
    return this.makeRequest('/api/tools/execute', {
      method: 'POST',
      body: JSON.stringify({
        tool: 'webSearch',
        service: 'mcphub',
        ...params,
      }),
    });
  }

  // Telegram service
  async sendTelegramMessage(params: TelegramParams): Promise<MCPResponse<{ messageId: number }>> {
    return this.makeRequest('/api/tools/execute', {
      method: 'POST',
      body: JSON.stringify({
        tool: 'sendMessage',
        service: 'mcphub',
        ...params,
      }),
    });
  }

  // Notion service
  async createNotionPage(params: NotionParams): Promise<MCPResponse<{ pageId: string }>> {
    return this.makeRequest('/api/tools/execute', {
      method: 'POST',
      body: JSON.stringify({
        tool: 'createPage',
        service: 'mcphub',
        ...params,
      }),
    });
  }

  // Generic tool execution
  async executeTool<T = any>(
    tool: string,
    params: any,
    service?: string
  ): Promise<MCPResponse<T>> {
    return this.makeRequest('/api/tools/execute', {
      method: 'POST',
      body: JSON.stringify({
        tool,
        service,
        ...params,
      }),
    });
  }

  // Direct service proxy
  async callService<T = any>(
    serviceName: string,
    endpoint: string,
    options: RequestInit = {}
  ): Promise<MCPResponse<T>> {
    return this.makeRequest(`/api/mcp/${serviceName}${endpoint}`, options);
  }

  // Batch operations
  async executeBatch<T = any>(
    operations: Array<{
      tool: string;
      params: any;
      service?: string;
    }>
  ): Promise<MCPResponse<T[]>> {
    const promises = operations.map(op =>
      this.executeTool(op.tool, op.params, op.service)
    );

    try {
      const results = await Promise.allSettled(promises);
      const data = results.map(result => 
        result.status === 'fulfilled' ? result.value.data : null
      );

      return {
        success: true,
        data,
        metadata: {
          service: 'mcp-gateway',
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Batch operation failed',
        metadata: {
          service: 'mcp-gateway',
          timestamp: new Date().toISOString(),
        },
      };
    }
  }

  // Real-time capabilities (WebSocket support)
  createWebSocket(endpoint: string = '/ws'): WebSocket | null {
    try {
      const wsUrl = this.baseUrl.replace('http', 'ws') + endpoint;
      const ws = new WebSocket(wsUrl);
      
      ws.onopen = () => {
        console.log('MCP WebSocket connected');
      };
      
      ws.onerror = (error) => {
        console.error('MCP WebSocket error:', error);
      };
      
      ws.onclose = () => {
        console.log('MCP WebSocket disconnected');
      };
      
      return ws;
    } catch (error) {
      console.error('Failed to create WebSocket:', error);
      return null;
    }
  }

  // Configuration
  setApiKey(apiKey: string): void {
    this.apiKey = apiKey;
  }

  setTimeout(timeout: number): void {
    this.timeout = timeout;
  }

  setRetryAttempts(attempts: number): void {
    this.retryAttempts = attempts;
  }

  getConfig(): {
    baseUrl: string;
    timeout: number;
    retryAttempts: number;
    hasApiKey: boolean;
  } {
    return {
      baseUrl: this.baseUrl,
      timeout: this.timeout,
      retryAttempts: this.retryAttempts,
      hasApiKey: !!this.apiKey,
    };
  }
}

// Create singleton instance
const mcpClient = new MCPClient();

export default mcpClient;
export { MCPClient };

// React hooks for MCP integration
export const useMCPServices = () => {
  const [services, setServices] = React.useState<MCPService[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    const fetchServices = async () => {
      try {
        const response = await mcpClient.getServices();
        if (response.success && response.data) {
          setServices(response.data.services);
        } else {
          setError(response.error || 'Failed to fetch services');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, []);

  return { services, loading, error, refetch: () => setLoading(true) };
};

export const useMCPHealth = () => {
  const [health, setHealth] = React.useState<any>(null);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    const checkHealth = async () => {
      const response = await mcpClient.getHealth();
      if (response.success) {
        setHealth(response.data);
      }
      setLoading(false);
    };

    checkHealth();
    const interval = setInterval(checkHealth, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, []);

  return { health, loading };
};

// Import React for hooks (this would normally be imported at the top)
import React from 'react';
