import { MCPServer } from 'model-context-protocol';
import { TextToSpeechClient } from '@azure/cognitiveservices-texttospeech';
import { CognitiveServicesCredentials } from '@azure/ms-rest-azure-js';

export async function startServer(port = 3020, azureKey: string, azureRegion: string) {
    const creds = new CognitiveServicesCredentials(azureKey);
    const client = new TextToSpeechClient(creds, azureRegion);
    const server = new MCPServer({ port });
    server.registerTool('azureTTS', {
        title: 'Azure Text-to-Speech',
        parameters: {
            type: 'object',
            properties: {
                text: { type: 'string' },
                voice: { type: 'string', default: 'en-US-JennyNeural' }
            },
            required: ['text']
        },
        returns: {
            type: 'object',
            properties: {
                audio_base64: { type: 'string' }
            }
        }
    }, async ({ text, voice }) => {
        const result = await client.textToSpeech({
            synthesisInput: { text },
            voice: { name: voice },
            audioConfig: { audioEncoding: 'Audio16Khz32KBitRateMonoMp3' }
        });
        const base64 = Buffer.from(await result.audio).toString('base64');
        return { audio_base64: base64 };
    });
    await server.listen();
}