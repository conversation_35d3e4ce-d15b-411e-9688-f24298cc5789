import { MCPServer } from 'model-context-protocol';
import { Client, GatewayIntentBits, TextChannel } from 'discord.js';

export async function startServer(port = 3012, token: string, channelId: string) {
    const client = new Client({ intents: [GatewayIntentBits.Guilds, GatewayIntentBits.GuildMessages] });
    await client.login(token);
    const channel = await client.channels.fetch(channelId) as TextChannel;
    const server = new MCPServer({ port });
    server.registerTool('sendDiscord', {
        title: 'Send Discord Message',
        parameters: { type: 'object', properties: { content: { type: 'string' } }, required: ['content'] },
        returns: { type: 'object', properties: { success: { type: 'boolean' } } }
    }, async ({ content }) => {
        await channel.send(content);
        return { success: true };
    });
    await server.listen();
}