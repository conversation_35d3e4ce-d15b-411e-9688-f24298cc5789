from fastapi import FastAP<PERSON>, HTTPException, Query
from github_official import GitHubMCP

app = FastAPI()
mcp = GitHubMCP()  # Add token optionally

@app.get("/repos/{username}")
def fetch_repos(username: str):
    try:
        return mcp.get_user_repos(username)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/create-repo")
def create_repo(name: str, private: bool = True):
    try:
        return mcp.create_repo(name, private)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
