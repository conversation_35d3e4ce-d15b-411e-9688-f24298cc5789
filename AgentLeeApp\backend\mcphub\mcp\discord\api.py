from fastapi import FastAP<PERSON>, HTTPException
from threading import Thread
from discord import DiscordMCP

app = FastAPI()

# Replace with your Discord bot token
BOT_TOKEN = "YOUR_DISCORD_BOT_TOKEN"
mcp = DiscordMCP(token=BOT_TOKEN)

@app.get("/discord/start")
def start_discord_bot():
    try:
        thread = Thread(target=mcp.run_bot, daemon=True)
        thread.start()
        return {"status": "Discord bot starting..."}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
