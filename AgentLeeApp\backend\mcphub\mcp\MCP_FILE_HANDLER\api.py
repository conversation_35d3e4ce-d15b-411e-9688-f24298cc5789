from fastapi import FastAP<PERSON>, HTTPException, Body
from MCP_FILE_HANDLER import FileHandlerMCP

mcp = FileHandlerMCP()
app = FastAPI()

@app.get("/file/read")
def read_file(path: str):
    try:
        return mcp.read_file(path)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/file/write")
def write_file(path: str = Body(...), content: str = Body(...)):
    try:
        return mcp.write_file(path, content)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/file/delete")
def delete_file(path: str):
    try:
        return mcp.delete_file(path)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/file/copy")
def copy_file(src: str = Body(...), dst: str = Body(...)):
    try:
        return mcp.copy_file(src, dst)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
