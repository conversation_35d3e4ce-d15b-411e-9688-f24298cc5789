from fastapi import FastAPI, HTTPException
from MCP_VOICE_LOOP import VoiceLoopMCP

mcp = VoiceLoopMCP()
app = FastAPI()

@app.get("/voice/start")
def start_loop():
    try:
        return mcp.start_loopback()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/voice/read")
def read_loop():
    try:
        return mcp.read_chunk()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/voice/stop")
def stop_loop():
    try:
        return mcp.stop_loopback()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
