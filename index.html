import React, { useEffect, useRef } from 'react';

const App: React.FC = () => {
  const videoRef = useRef<HTMLVideoElement | null>(null);

  useEffect(() => {
    const getCamera = async () => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ video: true });
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
        }
      } catch (err) {
        console.error("Camera access denied:", err);
      }
    };
    getCamera();
  }, []);

  const downloadDocument = () => {
    alert("Download initiated (connect to actual file source in logic).");
  };

  return (
    <div className="bg-gray-900 text-white font-sans min-h-screen px-10 py-6 max-w-7xl mx-auto">
      <h1 className="text-6xl font-extrabold text-center mb-12 bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent shadow-lg">
        Agent <PERSON>
      </h1>

      <div className="relative w-full mb-12 flex justify-between items-start">
        <div className="flex flex-col items-start text-left space-y-4">
          <img src="/assets/agentlee_avatar.png" alt="Agent Lee" className="w-56 h-56 rounded-full border-4 border-blue-500" />
          <div className="flex h-10 items-center justify-center">
            {Array.from({ length: 5 }).map((_, i) => (
              <span
                key={i}
                className="w-1.5 h-full mx-0.5 bg-blue-500 animate-pulse"
                style={{ animationDelay: `${-1.1 + i * 0.1}s` }}
              />
            ))}
          </div>
          <div className="bg-gray-800 p-4 rounded-xl w-80">
            <h2 className="text-lg font-semibold mb-2">Agent Lee Says:</h2>
            <p className="text-xl font-medium text-white">Here's what I found:</p>
          </div>
        </div>

        <div className="absolute right-0 top-0 transform -translate-y-8 md:translate-y-0">
          <div className="flex flex-col items-end">
            <video ref={videoRef} autoPlay muted playsInline className="w-72 h-72 rounded-xl shadow-xl bg-black object-cover" />
            <p className="text-sm text-gray-400 mt-2">Live Camera Feed</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 className="text-lg font-semibold mb-3">🌐 Website Results</h3>
          <div className="space-y-4">
            <div className="bg-gray-800 p-4 rounded-md hover:bg-gray-700 transition">
              <a href="#" className="block text-blue-400 font-semibold text-lg">Agent Lee – AI Voice Assistant Overview</a>
              <p className="text-gray-300 text-sm mt-1">Learn more about Agent Lee, your voice-powered AI assistant designed to guide, search, and teach in real-time.</p>
            </div>
            <div className="bg-gray-800 p-4 rounded-md hover:bg-gray-700 transition">
              <img src="https://via.placeholder.com/400x200.png?text=Web+Preview+Image" alt="Web Image" className="rounded-md mb-2" />
              <p className="text-sm text-gray-300">Example image result from a website. Click for more visual data or related documents.</p>
            </div>
            <div className="bg-gray-800 p-4 rounded-md hover:bg-gray-700 transition">
              <a href="#" className="block text-blue-400 font-semibold text-lg">Integrating Web Search with Voice Commands</a>
              <p className="text-gray-300 text-sm mt-1">How Agent Lee pulls in top-ranked pages and summaries directly into your AI dashboard.</p>
            </div>
          </div>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-3 flex justify-between items-center">
            📄 Document View
            <div className="space-x-2">
              <label className="bg-blue-600 px-3 py-1 rounded text-sm cursor-pointer hover:bg-blue-500">
                Upload
                <input type="file" id="fileUpload" className="hidden" multiple />
              </label>
              <button
                className="bg-green-600 px-3 py-1 rounded text-sm hover:bg-green-500"
                onClick={downloadDocument}
              >
                Download
              </button>
            </div>
          </h3>
          <div className="space-y-4">
            <div className="bg-gray-800 p-4 rounded-md">
              <p className="text-sm text-gray-400">From: <span className="text-white font-medium">training_manual.pdf</span></p>
              <h4 className="text-white font-semibold mt-2">How Agent Lee uses IndexedDB for offline memory</h4>
              <p className="text-gray-300 text-sm mt-1">Agent Lee stores personalized context locally, enabling persistent assistance even without network access...</p>
            </div>
            <div className="bg-gray-800 p-4 rounded-md">
              <p className="text-sm text-gray-400">From: <span className="text-white font-medium">echo_protocol_notes.docx</span></p>
              <h4 className="text-white font-semibold mt-2">Echo System Prompt Engineering</h4>
              <p className="text-gray-300 text-sm mt-1">This document outlines how Agent Lee combines speech memory diagnostics and document-sourced prompts into a dynamic reasoning cycle.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default App;

