from fastapi import Fast<PERSON><PERSON>, HTTPException, Body
from MCP_AZURE_TTS import AzureTTSMCP

# Replace these with your real Azure keys
mcp = AzureTTSMCP(subscription_key="YOUR_KEY_HERE", region="eastus")

app = FastAPI()

@app.post("/tts/speak")
def speak(text: str = Body(...), voice: str = "en-US-JennyNeural"):
    try:
        return mcp.synthesize(text, voice)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
