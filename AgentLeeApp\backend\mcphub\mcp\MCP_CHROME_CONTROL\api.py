from fastapi import FastAPI, HTTPException, Query
from MCP_CHROME_CONTROL import ChromeControlMCP

app = FastAPI()
mcp = ChromeControlMCP()

@app.get("/chrome/open")
def open_url(url: str):
    try:
        return mcp.open_url(url)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/chrome/elements")
def get_elements(tag: str = "p"):
    try:
        return mcp.get_elements_by_tag(tag)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/chrome/close")
def close_browser():
    try:
        return mcp.close_browser()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
