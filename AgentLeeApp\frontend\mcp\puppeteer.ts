/**
 * Puppeteer MCP Server with IoT Integration
 * Enables headless browser automation triggered by IoT events
 */

import { MCPServer, MCPTool, createToolSchema, ToolPropertyTypes } from '../mcp-base';
import axios from 'axios';

class PuppeteerIoTMCPServer extends MCPServer {
  private iotControllerUrl: string;

  constructor() {
    super({
      name: 'Puppeteer IoT MCP Server',
      version: '1.0.0',
      port: 3017
    });

    this.iotControllerUrl = 'http://localhost:3010';
    this.setupTools();
  }

  private setupTools(): void {
    // IoT-Triggered Puppeteer Action Tool
    const iotPuppeteerActionTool: MCPTool = {
      name: 'iotPuppeteerAction',
      description: 'Execute Puppeteer automation based on IoT device trigger',
      inputSchema: createToolSchema({
        deviceId: ToolPropertyTypes.string('IoT device ID that triggered the action'),
        action: ToolPropertyTypes.string('Puppeteer action to execute'),
        url: ToolPropertyTypes.string('URL to navigate to'),
        selector: ToolPropertyTypes.string('CSS selector for element interaction'),
        text: ToolPropertyTypes.string('Text to input or search for'),
        headless: ToolPropertyTypes.boolean('Run in headless mode', true)
      }, ['deviceId', 'action']),
      handler: async (params: any) => {
        return await this.executeIoTPuppeteerAction(params);
      }
    };

    // Process Voice Command Tool
    const processVoiceCommandTool: MCPTool = {
      name: 'processVoiceCommand',
      description: 'Process voice commands for Puppeteer automation with IoT context',
      inputSchema: createToolSchema({
        command: ToolPropertyTypes.string('Natural language command'),
        speaker: ToolPropertyTypes.string('Speaker/user identifier'),
        iotContext: ToolPropertyTypes.object('Current IoT device states', {})
      }, ['command']),
      handler: async (params: any) => {
        return await this.processVoiceCommand(params.command, params.speaker, params.iotContext);
      }
    };

    this.addTool(iotPuppeteerActionTool);
    this.addTool(processVoiceCommandTool);
  }

  private async executeIoTPuppeteerAction(params: any): Promise<any> {
    const { deviceId, action, url, selector, text, headless = true } = params;

    try {
      // Get device status from IoT controller
      const deviceResponse = await axios.get(`${this.iotControllerUrl}/devices/${deviceId}`);
      const device = deviceResponse.data;

      // Simulate Puppeteer action execution
      let result: any;
      switch (action) {
        case 'navigate':
          result = { action: 'navigated', url, headless, success: true };
          break;
        case 'click':
          result = { action: 'clicked', selector, success: true };
          break;
        case 'type':
          result = { action: 'typed', selector, text, success: true };
          break;
        case 'screenshot':
          result = { action: 'screenshot_taken', headless, success: true };
          break;
        case 'scrape':
          result = { action: 'data_scraped', selector, success: true };
          break;
        default:
          throw new Error(`Unsupported Puppeteer action: ${action}`);
      }

      return {
        success: true,
        deviceId,
        deviceName: device.name,
        puppeteerAction: action,
        result,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        success: false,
        error: `IoT Puppeteer action failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        deviceId,
        action
      };
    }
  }

  private async processVoiceCommand(command: string, speaker?: string, iotContext?: any): Promise<any> {
    const lowerCommand = command.toLowerCase();

    // Puppeteer command patterns with IoT context
    if (lowerCommand.includes('scrape') && lowerCommand.includes('when')) {
      return {
        success: true,
        message: 'Puppeteer IoT scraping command processed',
        command,
        speaker,
        iotContext,
        action: 'scraping_setup'
      };
    }

    if (lowerCommand.includes('headless') && lowerCommand.includes('automation')) {
      return {
        success: true,
        message: 'Puppeteer headless automation command processed',
        command,
        speaker,
        action: 'headless_automation'
      };
    }

    return {
      success: false,
      error: 'Could not understand Puppeteer command',
      command,
      suggestion: 'Try commands like "scrape data when sensor triggers" or "headless automation"'
    };
  }
}

// Export for use as module
export default PuppeteerIoTMCPServer;

// CLI support
if (require.main === module) {
  const server = new PuppeteerIoTMCPServer();
  server.start().then(() => {
    console.log('🎭 Puppeteer IoT MCP Server started');
  }).catch(console.error);
}
