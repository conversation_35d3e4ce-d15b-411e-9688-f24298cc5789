from fastapi import FastAPI, HTTPException, Query
from MCP_AGENT_STATUS_CHECK import AgentStatusCheckMCP

app = FastAPI()
mcp = AgentStatusCheckMCP()

@app.get("/agent/status")
def get_all_agent_status():
    try:
        return mcp.list_all()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/agent/ping")
def ping_agent(name: str = Query(...)):
    try:
        return mcp.ping_agent(name)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
