from fastapi import FastAP<PERSON>, HTTPException, Body
from MCP_AGENT_LEE_BRIDGE import AgentLeeBridgeMCP

app = FastAPI()
mcp = AgentLeeBridgeMCP()

@app.post("/agentlee/message")
def relay_message(message: str = Body(...)):
    try:
        return mcp.send_message_to_agent(message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/agentlee/command")
def relay_command(command: str = Body(...)):
    try:
        return mcp.relay_command(command)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
