import { MCPServer } from 'model-context-protocol';
import { Octokit } from 'octokit';

export async function startServer(port = 3028, githubToken: string) {
    const octokit = new Octokit({ auth: githubToken });
    const server = new MCPServer({ port });
    server.registerTool('createPR', {
        title: 'Create Pull Request',
        parameters: {
            type: 'object',
            properties: {
                owner: { type: 'string' },
                repo: { type: 'string' },
                title: { type: 'string' },
                head: { type: 'string' },
                base: { type: 'string' },
                body: { type: 'string' }
            },
            required: ['owner', 'repo', 'title', 'head', 'base']
        },
        returns: { type: 'object', properties: { number: { type: 'integer' }, url: { type: 'string' } } }
    }, async ({ owner, repo, title, head, base, body }) => {
        const resp = await octokit.rest.pulls.create({ owner, repo, title, head, base, body });
        return { number: resp.data.number, url: resp.data.html_url };
    });
    await server.listen();
}