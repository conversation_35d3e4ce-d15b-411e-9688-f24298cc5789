from fastapi import FastAPI, HTTPException, Body, Query
from MCP_INDEXEDDB_MEMORY import IndexedDBMemoryMCP

mcp = IndexedDBMemoryMCP()
app = FastAPI()

@app.post("/indexeddb/save")
def save_data(db: str = Body(...), key: str = Body(...), value = Body(...)):
    try:
        return mcp.save(db, key, value)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/indexeddb/retrieve")
def retrieve_data(db: str = Query(...), key: str = Query(...)):
    try:
        return mcp.retrieve(db, key)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/indexeddb/keys")
def list_keys(db: str = Query(...)):
    try:
        return mcp.list_keys(db)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
