import React from 'react';

interface OutputCardProps {
  title: string;
  className?: string;
  children: React.ReactNode;
}

export const OutputCard: React.FC<OutputCardProps> = ({ title, className = '', children }) => (
  <section className={`bg-gray-800 rounded-xl shadow-lg p-6 mb-4 ${className}`}>
    <h2 className="text-xl font-bold text-blue-300 mb-3">{title}</h2>
    <div>{children}</div>
  </section>
);
