import { MCPServer } from 'model-context-protocol';
import nodemailer from 'nodemailer';

export async function startServer(port = 3026, smtpConfig: any) {
    const transporter = nodemailer.createTransport(smtpConfig);
    const server = new MCPServer({ port });
    server.registerTool('sendEmail', {
        title: 'Email Sender',
        parameters: {
            type: 'object',
            properties: {
                to: { type: 'string' },
                subject: { type: 'string' },
                text: { type: 'string' },
                html: { type: 'string' }
            },
            required: ['to', 'subject', 'text']
        },
        returns: { type: 'object', properties: { messageId: { type: 'string' } } }
    }, async ({ to, subject, text, html }) => {
        const info = await transporter.sendMail({ to, subject, text, html });
        return { messageId: info.messageId };
    });
    await server.listen();
}