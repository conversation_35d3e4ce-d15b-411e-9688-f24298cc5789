from selenium import webdriver
from selenium.webdriver.chrome.options import Options
import os

class ScreenshotBrowserMCP:
    def __init__(self, headless=True, output_dir="screenshots"):
        os.makedirs(output_dir, exist_ok=True)
        self.output_dir = output_dir
        options = Options()
        if headless:
            options.add_argument("--headless=new")
        self.driver = webdriver.Chrome(options=options)

    def capture(self, url: str, filename: str = "screenshot.png"):
        try:
            self.driver.get(url)
            filepath = os.path.join(self.output_dir, filename)
            self.driver.save_screenshot(filepath)
            return {"status": "captured", "path": filepath}
        except Exception as e:
            return {"error": str(e)}

    def close(self):
        self.driver.quit()
