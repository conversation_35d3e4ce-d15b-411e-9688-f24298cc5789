from fastapi import FastAPI, HTTPException, Body, Query
from MCP_CALENDAR_MANAGER import CalendarManagerMCP

app = FastAPI()
mcp = CalendarManagerMCP()

@app.post("/calendar/create")
def create_event(title: str = Body(...), start_time: str = Body(...), duration_minutes: int = Body(60)):
    try:
        return mcp.create_event(title, start_time, duration_minutes)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/calendar/list")
def list_events():
    try:
        return mcp.list_events()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/calendar/delete")
def delete_event(event_id: str = Query(...)):
    try:
        return mcp.delete_event(event_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
