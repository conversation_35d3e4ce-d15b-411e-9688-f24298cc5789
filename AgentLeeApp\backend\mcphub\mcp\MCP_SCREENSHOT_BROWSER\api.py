from fastapi import FastAPI, HTTPException, Query
from MCP_SCREENSHOT_BROWSER import ScreenshotBrowserMCP

mcp = ScreenshotBrowserMCP()
app = FastAPI()

@app.get("/screenshot")
def capture_site(url: str = Query(...), filename: str = "screenshot.png"):
    try:
        return mcp.capture(url, filename)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/screenshot/close")
def close_browser():
    try:
        mcp.close()
        return {"status": "browser closed"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
