from fastapi import FastAPI, HTTPException, Body
from MCP_PHONE_CONTROL import PhoneControlMCP

mcp = PhoneControlMCP()
app = FastAPI()

@app.post("/phone/command")
def send_adb_command(cmd: str = Body(...)):
    try:
        return mcp.send_command(cmd)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/phone/call")
def make_call(number: str = Body(...)):
    try:
        return mcp.simulate_call(number)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
