import uuid
from datetime import datetime

class WebhookSystemMCP:
    def __init__(self):
        self.events = []

    def register_event(self, event_type: str, payload: dict):
        event = {
            "id": str(uuid.uuid4()),
            "timestamp": datetime.utcnow().isoformat(),
            "type": event_type,
            "payload": payload
        }
        self.events.append(event)
        return {"status": "received", "event": event}

    def list_events(self):
        return self.events
