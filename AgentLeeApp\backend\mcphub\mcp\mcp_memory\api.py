from fastapi import FastAPI, HTTPException, Body, Query
from memory import MemoryMCP

mcp = MemoryMCP()
app = FastAPI()

@app.post("/memory/store")
def store_memory(user_id: str = Query(...), key: str = Query(...), value = Body(...)):
    try:
        return mcp.store(user_id, key, value)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/memory/recall")
def recall_memory(user_id: str = Query(...), key: str = Query(...)):
    try:
        return mcp.recall(user_id, key)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/memory/dump")
def dump_memory(user_id: str = Query(...)):
    try:
        return mcp.dump(user_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
