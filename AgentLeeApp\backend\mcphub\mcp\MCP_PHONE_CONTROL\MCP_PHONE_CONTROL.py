import subprocess
import platform

class PhoneControlMCP:
    def __init__(self, adb_path="adb"):
        self.adb = adb_path

    def send_command(self, cmd: str):
        full_cmd = f"{self.adb} {cmd}"
        try:
            result = subprocess.run(full_cmd, shell=True, capture_output=True, text=True)
            return {
                "command": full_cmd,
                "stdout": result.stdout.strip(),
                "stderr": result.stderr.strip(),
                "returncode": result.returncode
            }
        except Exception as e:
            return {"error": str(e)}

    def simulate_call(self, number: str):
        return self.send_command(f'shell am start -a android.intent.action.CALL -d tel:{number}')
