/**
 * Notion MCP Server
 * Provides integration with Notion API for page and database management
 */

import { MCPServer, MCPTool, createToolSchema, ToolPropertyTypes } from '../mcp-base';

// Mock Notion client for development (replace with real @notionhq/client when available)
interface NotionClient {
  pages: {
    create: (params: any) => Promise<any>;
    retrieve: (params: any) => Promise<any>;
    update: (params: any) => Promise<any>;
  };
  databases: {
    query: (params: any) => Promise<any>;
    retrieve: (params: any) => Promise<any>;
  };
  blocks: {
    children: {
      append: (params: any) => Promise<any>;
      list: (params: any) => Promise<any>;
    };
  };
}

class MockNotionClient implements NotionClient {
  pages = {
    create: async (params: any) => ({
      id: `page_${Date.now()}`,
      created_time: new Date().toISOString(),
      properties: params.properties
    }),
    retrieve: async (params: any) => ({
      id: params.page_id,
      properties: {},
      created_time: new Date().toISOString()
    }),
    update: async (params: any) => ({
      id: params.page_id,
      properties: params.properties
    })
  };

  databases = {
    query: async (params: any) => ({
      results: [],
      has_more: false
    }),
    retrieve: async (params: any) => ({
      id: params.database_id,
      title: [{ plain_text: 'Mock Database' }]
    })
  };

  blocks = {
    children: {
      append: async (params: any) => ({
        results: params.children.map((child: any, index: number) => ({
          id: `block_${Date.now()}_${index}`,
          ...child
        }))
      }),
      list: async (params: any) => ({
        results: [],
        has_more: false
      })
    }
  };
}

class NotionMCPServer extends MCPServer {
  private notion: NotionClient;

  constructor(notionToken?: string) {
    super({
      name: 'Notion MCP Server',
      version: '1.0.0',
      port: 3009
    });

    // Use mock client if no token provided (for development)
    if (notionToken && notionToken !== 'mock') {
      try {
        // Try to import real Notion client
        const { Client } = require('@notionhq/client');
        this.notion = new Client({ auth: notionToken });
      } catch (error) {
        console.warn('Notion client not available, using mock client');
        this.notion = new MockNotionClient();
      }
    } else {
      this.notion = new MockNotionClient();
    }

    this.setupTools();
  }

  private setupTools(): void {
    // Create Page Tool
    const createPageTool: MCPTool = {
      name: 'createPage',
      description: 'Create a new page in Notion',
      inputSchema: createToolSchema({
        parentId: ToolPropertyTypes.string('Parent database or page ID'),
        title: ToolPropertyTypes.string('Page title'),
        content: ToolPropertyTypes.string('Page content (optional)', ''),
        properties: ToolPropertyTypes.object('Additional page properties', {})
      }, ['parentId', 'title']),
      handler: async ({ parentId, title, content = '', properties = {} }: {
        parentId: string;
        title: string;
        content?: string;
        properties?: Record<string, any>;
      }) => {
        try {
          const pageData: any = {
            parent: { database_id: parentId },
            properties: {
              title: {
                title: [{ text: { content: title } }]
              },
              ...properties
            }
          };

          const response = await this.notion.pages.create(pageData);

          // Add content if provided
          if (content) {
            await this.notion.blocks.children.append({
              block_id: response.id,
              children: [
                {
                  object: 'block',
                  type: 'paragraph',
                  paragraph: {
                    rich_text: [{ type: 'text', text: { content } }]
                  }
                }
              ]
            });
          }

          return {
            success: true,
            pageId: response.id,
            title,
            url: `https://notion.so/${response.id.replace(/-/g, '')}`
          };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          return {
            success: false,
            error: errorMessage
          };
        }
      }
    };

    // Query Database Tool
    const queryDatabaseTool: MCPTool = {
      name: 'queryDatabase',
      description: 'Query a Notion database',
      inputSchema: createToolSchema({
        databaseId: ToolPropertyTypes.string('Database ID to query'),
        filter: ToolPropertyTypes.object('Filter criteria', {}),
        sorts: ToolPropertyTypes.array('Sort criteria', {}),
        pageSize: ToolPropertyTypes.number('Number of results to return', 1, 100)
      }, ['databaseId']),
      handler: async ({ databaseId, filter = {}, sorts = [], pageSize = 10 }: {
        databaseId: string;
        filter?: any;
        sorts?: any[];
        pageSize?: number;
      }) => {
        try {
          const response = await this.notion.databases.query({
            database_id: databaseId,
            filter,
            sorts,
            page_size: pageSize
          });

          return {
            success: true,
            results: response.results,
            hasMore: response.has_more,
            count: response.results.length
          };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          return {
            success: false,
            error: errorMessage
          };
        }
      }
    };

    // Update Page Tool
    const updatePageTool: MCPTool = {
      name: 'updatePage',
      description: 'Update an existing Notion page',
      inputSchema: createToolSchema({
        pageId: ToolPropertyTypes.string('Page ID to update'),
        properties: ToolPropertyTypes.object('Properties to update', {}),
        archived: ToolPropertyTypes.boolean('Whether to archive the page', false)
      }, ['pageId']),
      handler: async ({ pageId, properties = {}, archived = false }: {
        pageId: string;
        properties?: Record<string, any>;
        archived?: boolean;
      }) => {
        try {
          const response = await this.notion.pages.update({
            page_id: pageId,
            properties,
            archived
          });

          return {
            success: true,
            pageId: response.id,
            archived
          };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          return {
            success: false,
            error: errorMessage
          };
        }
      }
    };

    // Add Content to Page Tool
    const addContentTool: MCPTool = {
      name: 'addContent',
      description: 'Add content blocks to a Notion page',
      inputSchema: createToolSchema({
        pageId: ToolPropertyTypes.string('Page ID to add content to'),
        content: ToolPropertyTypes.string('Content to add'),
        blockType: ToolPropertyTypes.string('Type of block (paragraph, heading_1, heading_2, etc.)', 'paragraph')
      }, ['pageId', 'content']),
      handler: async ({ pageId, content, blockType = 'paragraph' }: {
        pageId: string;
        content: string;
        blockType?: string;
      }) => {
        try {
          const blockData: any = {
            object: 'block',
            type: blockType,
            [blockType]: {
              rich_text: [{ type: 'text', text: { content } }]
            }
          };

          const response = await this.notion.blocks.children.append({
            block_id: pageId,
            children: [blockData]
          });

          return {
            success: true,
            pageId,
            blocksAdded: response.results.length,
            blockType
          };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          return {
            success: false,
            error: errorMessage
          };
        }
      }
    };

    // Add all tools
    this.addTool(createPageTool);
    this.addTool(queryDatabaseTool);
    this.addTool(updatePageTool);
    this.addTool(addContentTool);
  }

  protected override getMetrics(): any {
    const baseMetrics = super.getMetrics();
    return {
      ...baseMetrics,
      notionClientType: this.notion instanceof MockNotionClient ? 'mock' : 'real'
    };
  }
}

// Export for use in other modules
export async function startServer(port = 3009, notionToken?: string): Promise<NotionMCPServer> {
  const server = new NotionMCPServer(notionToken);
  await server.start(port);
  return server;
}

// Start server if this file is run directly
if (require.main === module) {
  const notionToken = process.env.NOTION_TOKEN || 'mock';
  startServer(3009, notionToken).catch(console.error);
}
