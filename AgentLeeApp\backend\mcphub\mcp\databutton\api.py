from fastapi import FastAP<PERSON>, HTTPException, Body
from databutton import DataButtonMCP

app = FastAPI()
mcp = DataButtonMCP()

@app.get("/databutton/apps")
def get_apps():
    try:
        return mcp.list_apps()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/databutton/run")
def run_app(app_id: str, payload: dict = Body(...)):
    try:
        return mcp.run_app(app_id, payload)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/databutton/status/{run_id}")
def get_status(run_id: str):
    try:
        return mcp.get_status(run_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
