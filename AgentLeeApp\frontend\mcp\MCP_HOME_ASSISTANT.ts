import { MCPServer } from '../mcp-base';
import { MCPTool, MCPResource, ToolPropertyTypes, createToolSchema } from '../mcp-base';
import { Request, Response } from 'express';
import axios from 'axios';
import WebSocket from 'ws';

interface HomeAssistantConfig {
  url: string;
  token: string;
  websocketUrl: string;
  enabled: boolean;
}

interface HAEntity {
  entity_id: string;
  state: string;
  attributes: Record<string, any>;
  last_changed: string;
  last_updated: string;
  context: {
    id: string;
    parent_id?: string;
    user_id?: string;
  };
}

interface HAService {
  domain: string;
  service: string;
  target?: {
    entity_id?: string | string[];
    device_id?: string | string[];
    area_id?: string | string[];
  };
  service_data?: Record<string, any>;
}

class HomeAssistantMCPServer extends MCPServer {
  private config: HomeAssistantConfig;
  private entities: Map<string, HAEntity> = new Map();
  private websocket?: WebSocket;
  private wsMessageId = 1;
  private isConnected = false;

  constructor(port: number = 3011) {
    super({
      name: 'Home Assistant MCP Server',
      version: '1.0.0',
      port,
      enableMetrics: true,
      enableHealthCheck: true
    });

    this.config = {
      url: process.env.HOME_ASSISTANT_URL || 'http://homeassistant.local:8123',
      token: process.env.HOME_ASSISTANT_TOKEN || '',
      websocketUrl: process.env.HOME_ASSISTANT_WS_URL || 'ws://homeassistant.local:8123/api/websocket',
      enabled: process.env.HOME_ASSISTANT_ENABLED === 'true'
    };

    this.initializeTools();
    this.initializeResources();
    this.initializeConnection();
  }

  private initializeTools() {
    // Call Service Tool
    const callServiceTool: MCPTool = {
      name: 'callService',
      description: 'Call a Home Assistant service (turn on lights, lock doors, etc.)',
      inputSchema: createToolSchema({
        domain: ToolPropertyTypes.string('Service domain (light, switch, lock, etc.)'),
        service: ToolPropertyTypes.string('Service name (turn_on, turn_off, toggle, etc.)'),
        entity_id: ToolPropertyTypes.string('Target entity ID or comma-separated list', false),
        service_data: ToolPropertyTypes.object('Additional service data', false)
      }),
      handler: async (params: any) => {
        return await this.callService(params.domain, params.service, params.entity_id, params.service_data);
      }
    };

    // Get Entity State Tool
    const getEntityStateTool: MCPTool = {
      name: 'getEntityState',
      description: 'Get the current state and attributes of a Home Assistant entity',
      inputSchema: createToolSchema({
        entity_id: ToolPropertyTypes.string('Entity ID to query')
      }),
      handler: async (params: any) => {
        return await this.getEntityState(params.entity_id);
      }
    };

    // List Entities Tool
    const listEntitesTool: MCPTool = {
      name: 'listEntities',
      description: 'List all entities or filter by domain/area',
      inputSchema: createToolSchema({
        domain: ToolPropertyTypes.string('Filter by domain (light, switch, sensor, etc.)', false),
        area: ToolPropertyTypes.string('Filter by area name', false),
        state: ToolPropertyTypes.string('Filter by state (on, off, etc.)', false)
      }),
      handler: async (params: any) => {
        return await this.listEntities(params.domain, params.area, params.state);
      }
    };

    // Control Light Tool
    const controlLightTool: MCPTool = {
      name: 'controlLight',
      description: 'Control smart lights with advanced options (brightness, color, etc.)',
      inputSchema: createToolSchema({
        entity_id: ToolPropertyTypes.string('Light entity ID'),
        action: ToolPropertyTypes.string('Action: on, off, toggle'),
        brightness: ToolPropertyTypes.number('Brightness (0-255)', false),
        color_name: ToolPropertyTypes.string('Color name (red, blue, etc.)', false),
        rgb_color: ToolPropertyTypes.array('RGB color [r, g, b]', false),
        transition: ToolPropertyTypes.number('Transition time in seconds', false)
      }),
      handler: async (params: any) => {
        return await this.controlLight(params);
      }
    };

    // Control Climate Tool
    const controlClimateTool: MCPTool = {
      name: 'controlClimate',
      description: 'Control thermostats and climate devices',
      inputSchema: createToolSchema({
        entity_id: ToolPropertyTypes.string('Climate entity ID'),
        temperature: ToolPropertyTypes.number('Target temperature', false),
        hvac_mode: ToolPropertyTypes.string('HVAC mode (heat, cool, auto, off)', false),
        fan_mode: ToolPropertyTypes.string('Fan mode', false)
      }),
      handler: async (params: any) => {
        return await this.controlClimate(params);
      }
    };

    // Execute Automation Tool
    const executeAutomationTool: MCPTool = {
      name: 'executeAutomation',
      description: 'Trigger a Home Assistant automation',
      inputSchema: createToolSchema({
        entity_id: ToolPropertyTypes.string('Automation entity ID'),
        variables: ToolPropertyTypes.object('Variables to pass to automation', false)
      }),
      handler: async (params: any) => {
        return await this.executeAutomation(params.entity_id, params.variables);
      }
    };

    // Get Areas Tool
    const getAreasTool: MCPTool = {
      name: 'getAreas',
      description: 'Get all areas/rooms configured in Home Assistant',
      inputSchema: createToolSchema({}),
      handler: async (params: any) => {
        return await this.getAreas();
      }
    };

    // Voice Command Tool
    const processVoiceCommandTool: MCPTool = {
      name: 'processHomeAssistantVoiceCommand',
      description: 'Process natural language commands for Home Assistant devices',
      inputSchema: createToolSchema({
        command: ToolPropertyTypes.string('Natural language command'),
        area: ToolPropertyTypes.string('Target area/room', false)
      }),
      handler: async (params: any) => {
        return await this.processVoiceCommand(params.command, params.area);
      }
    };

    // Register all tools
    this.addTool(callServiceTool);
    this.addTool(getEntityStateTool);
    this.addTool(listEntitesTool);
    this.addTool(controlLightTool);
    this.addTool(controlClimateTool);
    this.addTool(executeAutomationTool);
    this.addTool(getAreasTool);
    this.addTool(processVoiceCommandTool);
  }

  private initializeResources() {
    // Entities Resource
    const entitiesResource: MCPResource = {
      uri: 'homeassistant://entities',
      name: 'All Entities',
      description: 'Complete list of Home Assistant entities',
      mimeType: 'application/json'
    };

    // Lights Resource
    const lightsResource: MCPResource = {
      uri: 'homeassistant://lights',
      name: 'Light Entities',
      description: 'All light entities in Home Assistant',
      mimeType: 'application/json'
    };

    // Sensors Resource
    const sensorsResource: MCPResource = {
      uri: 'homeassistant://sensors',
      name: 'Sensor Entities',
      description: 'All sensor entities in Home Assistant',
      mimeType: 'application/json'
    };

    // Areas Resource
    const areasResource: MCPResource = {
      uri: 'homeassistant://areas',
      name: 'Areas/Rooms',
      description: 'All areas and rooms configured in Home Assistant',
      mimeType: 'application/json'
    };

    // Automations Resource
    const automationsResource: MCPResource = {
      uri: 'homeassistant://automations',
      name: 'Automations',
      description: 'All automations in Home Assistant',
      mimeType: 'application/json'
    };

    // System Info Resource
    const systemInfoResource: MCPResource = {
      uri: 'homeassistant://system-info',
      name: 'System Information',
      description: 'Home Assistant system information and status',
      mimeType: 'application/json'
    };

    this.addResource(entitiesResource);
    this.addResource(lightsResource);
    this.addResource(sensorsResource);
    this.addResource(areasResource);
    this.addResource(automationsResource);
    this.addResource(systemInfoResource);
  }

  private async initializeConnection() {
    if (!this.config.enabled || !this.config.token) {
      console.warn('⚠️ Home Assistant not configured or disabled');
      return;
    }

    try {
      // Test REST API connection
      await this.testConnection();
      
      // Initialize WebSocket connection for real-time updates
      await this.initializeWebSocket();
      
      // Load initial entities
      await this.loadEntities();
      
      console.log('🏠 Home Assistant integration initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Home Assistant connection:', error);
    }
  }

  private async testConnection(): Promise<void> {
    const response = await axios.get(`${this.config.url}/api/`, {
      headers: {
        'Authorization': `Bearer ${this.config.token}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });

    if (response.status !== 200) {
      throw new Error(`Home Assistant API returned status ${response.status}`);
    }

    console.log('✅ Home Assistant REST API connection successful');
  }

  private async initializeWebSocket(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.websocket = new WebSocket(this.config.websocketUrl);

      this.websocket.on('open', () => {
        console.log('🔌 Home Assistant WebSocket connected');
      });

      this.websocket.on('message', (data: WebSocket.Data) => {
        try {
          const message = JSON.parse(data.toString());
          this.handleWebSocketMessage(message);
          
          if (message.type === 'auth_required') {
            // Send authentication
            this.websocket?.send(JSON.stringify({
              type: 'auth',
              access_token: this.config.token
            }));
          } else if (message.type === 'auth_ok') {
            this.isConnected = true;
            this.subscribeToStateChanges();
            resolve();
          } else if (message.type === 'auth_invalid') {
            reject(new Error('Home Assistant authentication failed'));
          }
        } catch (error) {
          console.error('❌ WebSocket message parsing error:', error);
        }
      });

      this.websocket.on('error', (error) => {
        console.error('❌ Home Assistant WebSocket error:', error);
        reject(error);
      });

      this.websocket.on('close', () => {
        console.log('🔌 Home Assistant WebSocket disconnected');
        this.isConnected = false;
        // Attempt to reconnect after 5 seconds
        setTimeout(() => this.initializeWebSocket(), 5000);
      });
    });
  }

  private subscribeToStateChanges(): void {
    if (!this.websocket || !this.isConnected) return;

    this.websocket.send(JSON.stringify({
      id: this.wsMessageId++,
      type: 'subscribe_events',
      event_type: 'state_changed'
    }));
  }

  private handleWebSocketMessage(message: any): void {
    if (message.type === 'event' && message.event?.event_type === 'state_changed') {
      const newState = message.event.data.new_state;
      if (newState) {
        this.entities.set(newState.entity_id, newState);
      }
    }
  }

  private async makeHARequest(endpoint: string, method: 'GET' | 'POST' = 'GET', data?: any): Promise<any> {
    try {
      const config: any = {
        method,
        url: `${this.config.url}${endpoint}`,
        headers: {
          'Authorization': `Bearer ${this.config.token}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      };

      if (data && method === 'POST') {
        config.data = data;
      }

      const response = await axios(config);
      return {
        success: true,
        data: response.data,
        status: response.status
      };

    } catch (error) {
      if (axios.isAxiosError(error)) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          data: error.response?.data
        };
      }
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async loadEntities(): Promise<void> {
    try {
      const result = await this.makeHARequest('/api/states');

      if (result.success && Array.isArray(result.data)) {
        result.data.forEach((entity: HAEntity) => {
          this.entities.set(entity.entity_id, entity);
        });
        console.log(`📊 Loaded ${this.entities.size} Home Assistant entities`);
      }
    } catch (error) {
      console.error('❌ Failed to load entities:', error);
    }
  }

  private async callService(domain: string, service: string, entityId?: string, serviceData?: any): Promise<any> {
    try {
      const payload: HAService = {
        domain,
        service
      };

      if (entityId) {
        payload.target = { entity_id: entityId };
      }

      if (serviceData) {
        payload.service_data = serviceData;
      }

      const result = await this.makeHARequest(`/api/services/${domain}/${service}`, 'POST', payload);

      return {
        success: result.success,
        service: `${domain}.${service}`,
        target: entityId,
        result: result.data,
        error: result.success ? undefined : result.error
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Service call failed'
      };
    }
  }

  private async getEntityState(entityId: string): Promise<any> {
    try {
      // First check local cache
      const cachedEntity = this.entities.get(entityId);
      if (cachedEntity) {
        return {
          success: true,
          entity_id: entityId,
          state: cachedEntity.state,
          attributes: cachedEntity.attributes,
          last_changed: cachedEntity.last_changed,
          last_updated: cachedEntity.last_updated
        };
      }

      // If not in cache, fetch from API
      const result = await this.makeHARequest(`/api/states/${entityId}`);

      if (result.success) {
        // Update cache
        this.entities.set(entityId, result.data);

        return {
          success: true,
          entity_id: entityId,
          state: result.data.state,
          attributes: result.data.attributes,
          last_changed: result.data.last_changed,
          last_updated: result.data.last_updated
        };
      }

      return {
        success: false,
        error: result.error || 'Entity not found'
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get entity state'
      };
    }
  }

  private async listEntities(domain?: string, area?: string, state?: string): Promise<any> {
    try {
      let entities = Array.from(this.entities.values());

      // Filter by domain
      if (domain) {
        entities = entities.filter(entity => entity.entity_id.startsWith(`${domain}.`));
      }

      // Filter by area
      if (area) {
        entities = entities.filter(entity =>
          entity.attributes.area_id === area ||
          entity.attributes.friendly_name?.toLowerCase().includes(area.toLowerCase())
        );
      }

      // Filter by state
      if (state) {
        entities = entities.filter(entity => entity.state === state);
      }

      return {
        success: true,
        total_entities: this.entities.size,
        filtered_count: entities.length,
        entities: entities.map(entity => ({
          entity_id: entity.entity_id,
          state: entity.state,
          friendly_name: entity.attributes.friendly_name || entity.entity_id,
          domain: entity.entity_id.split('.')[0],
          area: entity.attributes.area_id,
          last_changed: entity.last_changed
        }))
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to list entities'
      };
    }
  }

  private async controlLight(params: any): Promise<any> {
    try {
      const { entity_id, action, brightness, color_name, rgb_color, transition } = params;

      let service: string;
      let serviceData: any = {};

      switch (action.toLowerCase()) {
        case 'on':
        case 'turn_on':
          service = 'turn_on';
          if (brightness !== undefined) serviceData.brightness = brightness;
          if (color_name) serviceData.color_name = color_name;
          if (rgb_color) serviceData.rgb_color = rgb_color;
          if (transition !== undefined) serviceData.transition = transition;
          break;

        case 'off':
        case 'turn_off':
          service = 'turn_off';
          if (transition !== undefined) serviceData.transition = transition;
          break;

        case 'toggle':
          service = 'toggle';
          break;

        default:
          return {
            success: false,
            error: `Unsupported light action: ${action}`
          };
      }

      return await this.callService('light', service, entity_id, serviceData);

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Light control failed'
      };
    }
  }

  private async controlClimate(params: any): Promise<any> {
    try {
      const { entity_id, temperature, hvac_mode, fan_mode } = params;

      let serviceData: any = {};

      if (temperature !== undefined) serviceData.temperature = temperature;
      if (hvac_mode) serviceData.hvac_mode = hvac_mode;
      if (fan_mode) serviceData.fan_mode = fan_mode;

      return await this.callService('climate', 'set_temperature', entity_id, serviceData);

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Climate control failed'
      };
    }
  }

  private async executeAutomation(entityId: string, variables?: any): Promise<any> {
    try {
      const serviceData = variables ? { variables } : {};
      return await this.callService('automation', 'trigger', entityId, serviceData);

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Automation execution failed'
      };
    }
  }

  private async getAreas(): Promise<any> {
    try {
      const result = await this.makeHARequest('/api/config/area_registry');

      return {
        success: result.success,
        areas: result.data || [],
        count: result.data?.length || 0,
        error: result.success ? undefined : result.error
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get areas'
      };
    }
  }

  private async processVoiceCommand(command: string, area?: string): Promise<any> {
    try {
      const normalizedCommand = command.toLowerCase().trim();

      // Parse natural language commands for Home Assistant
      const commandPatterns = [
        {
          pattern: /turn (on|off) (.+)/,
          action: async (matches: RegExpMatchArray) => {
            const action = matches[1];
            const deviceName = matches[2];
            const entities = this.findEntitiesByName(deviceName, area);

            if (entities.length === 0) {
              return { success: false, error: `No devices found matching "${deviceName}"` };
            }

            const results = [];
            for (const entity of entities) {
              const domain = entity.entity_id.split('.')[0];
              const service = action === 'on' ? 'turn_on' : 'turn_off';
              const result = await this.callService(domain, service, entity.entity_id);
              results.push(result);
            }

            return {
              success: true,
              action: `turn_${action}`,
              devices: entities.map(e => e.entity_id),
              results
            };
          }
        },
        {
          pattern: /set (.+) to (\d+)%?/,
          action: async (matches: RegExpMatchArray) => {
            const deviceName = matches[1];
            const value = parseInt(matches[2]);
            const entities = this.findEntitiesByName(deviceName, area);

            if (entities.length === 0) {
              return { success: false, error: `No devices found matching "${deviceName}"` };
            }

            const results = [];
            for (const entity of entities) {
              if (entity.entity_id.startsWith('light.')) {
                const brightness = Math.round((value / 100) * 255);
                const result = await this.controlLight({
                  entity_id: entity.entity_id,
                  action: 'on',
                  brightness
                });
                results.push(result);
              } else if (entity.entity_id.startsWith('climate.')) {
                const result = await this.controlClimate({
                  entity_id: entity.entity_id,
                  temperature: value
                });
                results.push(result);
              }
            }

            return {
              success: true,
              action: 'set_value',
              value,
              devices: entities.map(e => e.entity_id),
              results
            };
          }
        },
        {
          pattern: /(dim|brighten) (.+)/,
          action: async (matches: RegExpMatchArray) => {
            const action = matches[1];
            const deviceName = matches[2];
            const entities = this.findEntitiesByName(deviceName, area).filter(e =>
              e.entity_id.startsWith('light.')
            );

            if (entities.length === 0) {
              return { success: false, error: `No lights found matching "${deviceName}"` };
            }

            const brightness = action === 'dim' ? 77 : 204; // 30% or 80%
            const results = [];

            for (const entity of entities) {
              const result = await this.controlLight({
                entity_id: entity.entity_id,
                action: 'on',
                brightness
              });
              results.push(result);
            }

            return {
              success: true,
              action,
              devices: entities.map(e => e.entity_id),
              results
            };
          }
        }
      ];

      // Try to match command patterns
      for (const pattern of commandPatterns) {
        const matches = normalizedCommand.match(pattern.pattern);
        if (matches) {
          return await pattern.action(matches);
        }
      }

      return {
        success: false,
        error: `Could not understand command: "${command}"`,
        suggestion: 'Try commands like "turn on living room lights" or "set thermostat to 72"'
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Voice command processing failed'
      };
    }
  }

  private findEntitiesByName(name: string, area?: string): HAEntity[] {
    const normalizedName = name.toLowerCase();
    let entities = Array.from(this.entities.values());

    // Filter by area if specified
    if (area) {
      entities = entities.filter(entity =>
        entity.attributes.area_id === area ||
        entity.attributes.friendly_name?.toLowerCase().includes(area.toLowerCase())
      );
    }

    // Find entities matching the name
    return entities.filter(entity => {
      const friendlyName = entity.attributes.friendly_name?.toLowerCase() || '';
      const entityId = entity.entity_id.toLowerCase();

      return friendlyName.includes(normalizedName) ||
             entityId.includes(normalizedName.replace(/\s+/g, '_'));
    });
  }

  protected override async getResourceContent(resource: any): Promise<any> {
    try {
      switch (resource.uri) {
        case 'homeassistant://entities':
          return Array.from(this.entities.values());

        case 'homeassistant://lights':
          return Array.from(this.entities.values()).filter(e => e.entity_id.startsWith('light.'));

        case 'homeassistant://sensors':
          return Array.from(this.entities.values()).filter(e => e.entity_id.startsWith('sensor.'));

        case 'homeassistant://areas':
          const areasResult = await this.getAreas();
          return areasResult.success ? areasResult.areas : [];

        case 'homeassistant://automations':
          return Array.from(this.entities.values()).filter(e => e.entity_id.startsWith('automation.'));

        case 'homeassistant://system-info':
          const systemResult = await this.makeHARequest('/api/config');
          return {
            config: systemResult.success ? systemResult.data : null,
            connection_status: this.isConnected ? 'connected' : 'disconnected',
            entities_count: this.entities.size,
            websocket_connected: this.isConnected,
            last_update: new Date()
          };

        default:
          return null;
      }
    } catch (error) {
      console.error(`❌ Failed to get resource content for ${resource.uri}:`, error);
      return null;
    }
  }

  override async stop(): Promise<void> {
    console.log('🏠 Shutting down Home Assistant MCP Server...');

    if (this.websocket) {
      this.websocket.close();
    }

    await super.stop();
  }
}

// Export the server class and start function
export { HomeAssistantMCPServer };

export async function startHomeAssistantServer(port: number = 3011): Promise<HomeAssistantMCPServer> {
  const server = new HomeAssistantMCPServer(port);
  await server.start();
  console.log(`🏠 Home Assistant MCP Server started on port ${port}`);
  return server;
}

// Start server if this file is run directly
if (require.main === module) {
  startHomeAssistantServer().catch(console.error);
}
