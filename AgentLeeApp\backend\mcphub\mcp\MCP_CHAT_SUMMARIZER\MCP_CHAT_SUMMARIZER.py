from transformers import pipeline

class ChatSummarizerMCP:
    def __init__(self, model_name="sshleifer/distilbart-cnn-12-6"):
        self.summarizer = pipeline("summarization", model=model_name)

    def summarize(self, text: str, max_length: int = 150, min_length: int = 40):
        if not text.strip():
            return {"error": "Empty input"}
        summary = self.summarizer(text, max_length=max_length, min_length=min_length, do_sample=False)
        return {"summary": summary[0]["summary_text"]}
