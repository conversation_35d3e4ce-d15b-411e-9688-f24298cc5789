import psutil
import platform
import datetime

class HealthMonitorMCP:
    def get_system_health(self):
        return {
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory": psutil.virtual_memory()._asdict(),
            "disk": psutil.disk_usage("/")._asdict(),
            "boot_time": str(datetime.datetime.fromtimestamp(psutil.boot_time())),
            "platform": platform.platform(),
            "uptime_minutes": round((datetime.datetime.now() - datetime.datetime.fromtimestamp(psutil.boot_time())).total_seconds() / 60, 2)
        }

    def list_processes(self, limit=10):
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent']):
            try:
                processes.append(proc.info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return sorted(processes, key=lambda x: x['cpu_percent'], reverse=True)[:limit]
