from telegram import Bo<PERSON>, Update
from telegram.ext import <PERSON><PERSON><PERSON><PERSON>, MessageH<PERSON>ler, <PERSON>lters, Updater
import threading

class TelegramBotMCP:
    def __init__(self, token: str):
        self.token = token
        self.updater = Updater(token=self.token, use_context=True)
        self.dispatcher = self.updater.dispatcher
        self.thread = None

        # Define command handler
        self.dispatcher.add_handler(CommandHandler("start", self.start))
        self.dispatcher.add_handler(MessageHandler(Filters.text & ~Filters.command, self.echo))

    def start(self, update: Update, context):
        context.bot.send_message(chat_id=update.effective_chat.id, text="🤖 <PERSON> is ready!")

    def echo(self, update: Update, context):
        context.bot.send_message(chat_id=update.effective_chat.id, text=f"You said: {update.message.text}")

    def run_bot(self):
        self.thread = threading.Thread(target=self.updater.start_polling)
        self.thread.daemon = True
        self.thread.start()
        return {"status": "bot running in background"}

    def stop_bot(self):
        self.updater.stop()
        if self.thread:
            self.thread.join()
        return {"status": "bot stopped"}
