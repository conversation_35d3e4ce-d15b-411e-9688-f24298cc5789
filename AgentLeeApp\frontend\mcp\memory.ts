/**
 * Memory MCP Server
 * Provides persistent memory storage for AI agents
 */

import { <PERSON><PERSON>erver, MCPTool, createToolSchema, ToolPropertyTypes } from '../mcp-base';

interface MemoryEntry {
  key: string;
  value: any;
  timestamp: Date;
  metadata?: Record<string, any>;
}

class MemoryMCPServer extends MCPServer {
  private store: Map<string, MemoryEntry> = new Map();

  constructor() {
    super({
      name: 'Memory MCP Server',
      version: '1.0.0',
      port: 3007
    });

    this.setupTools();
  }

  private setupTools(): void {
    // Get Memory Tool
    const getMemoryTool: MCPTool = {
      name: 'getMemory',
      description: 'Retrieve a value from memory by key',
      inputSchema: createToolSchema({
        key: ToolPropertyTypes.string('The key to retrieve from memory')
      }, ['key']),
      handler: async ({ key }: { key: string }) => {
        const entry = this.store.get(key);
        return {
          key,
          value: entry?.value ?? null,
          timestamp: entry?.timestamp,
          metadata: entry?.metadata,
          found: !!entry
        };
      }
    };

    // Set Memory Tool
    const setMemoryTool: MCPTool = {
      name: 'setMemory',
      description: 'Store a value in memory with a key',
      inputSchema: createToolSchema({
        key: ToolPropertyTypes.string('The key to store the value under'),
        value: ToolPropertyTypes.string('The value to store'),
        metadata: ToolPropertyTypes.object('Optional metadata for the memory entry', {})
      }, ['key', 'value']),
      handler: async ({ key, value, metadata }: { key: string; value: any; metadata?: Record<string, any> }) => {
        const entry: MemoryEntry = {
          key,
          value,
          timestamp: new Date(),
          metadata
        };

        this.store.set(key, entry);

        return {
          success: true,
          key,
          timestamp: entry.timestamp
        };
      }
    };

    // Delete Memory Tool
    const deleteMemoryTool: MCPTool = {
      name: 'deleteMemory',
      description: 'Delete a memory entry by key',
      inputSchema: createToolSchema({
        key: ToolPropertyTypes.string('The key to delete from memory')
      }, ['key']),
      handler: async ({ key }: { key: string }) => {
        const existed = this.store.has(key);
        this.store.delete(key);

        return {
          success: true,
          key,
          existed
        };
      }
    };

    // List Memory Keys Tool
    const listMemoryTool: MCPTool = {
      name: 'listMemory',
      description: 'List all memory keys with optional filtering',
      inputSchema: createToolSchema({
        pattern: ToolPropertyTypes.string('Optional regex pattern to filter keys', '.*')
      }),
      handler: async ({ pattern = '.*' }: { pattern?: string }) => {
        const regex = new RegExp(pattern);
        const entries = Array.from(this.store.entries())
          .filter(([key]) => regex.test(key))
          .map(([key, entry]) => ({
            key,
            timestamp: entry.timestamp,
            hasMetadata: !!entry.metadata
          }));

        return {
          entries,
          total: entries.length,
          pattern
        };
      }
    };

    // Clear Memory Tool
    const clearMemoryTool: MCPTool = {
      name: 'clearMemory',
      description: 'Clear all memory entries',
      inputSchema: createToolSchema({
        confirm: ToolPropertyTypes.boolean('Confirmation to clear all memory', false)
      }, ['confirm']),
      handler: async ({ confirm }: { confirm: boolean }) => {
        if (!confirm) {
          return {
            success: false,
            error: 'Confirmation required to clear all memory'
          };
        }

        const count = this.store.size;
        this.store.clear();

        return {
          success: true,
          clearedCount: count
        };
      }
    };

    // Add all tools
    this.addTool(getMemoryTool);
    this.addTool(setMemoryTool);
    this.addTool(deleteMemoryTool);
    this.addTool(listMemoryTool);
    this.addTool(clearMemoryTool);
  }

  protected override getMetrics(): any {
    const baseMetrics = super.getMetrics();
    return {
      ...baseMetrics,
      memoryEntries: this.store.size,
      memoryKeys: Array.from(this.store.keys()),
      oldestEntry: this.getOldestEntry(),
      newestEntry: this.getNewestEntry()
    };
  }

  private getOldestEntry(): Date | null {
    if (this.store.size === 0) return null;

    let oldest: Date | null = null;
    for (const entry of this.store.values()) {
      if (!oldest || entry.timestamp < oldest) {
        oldest = entry.timestamp;
      }
    }
    return oldest;
  }

  private getNewestEntry(): Date | null {
    if (this.store.size === 0) return null;

    let newest: Date | null = null;
    for (const entry of this.store.values()) {
      if (!newest || entry.timestamp > newest) {
        newest = entry.timestamp;
      }
    }
    return newest;
  }
}

// Export for use in other modules
export async function startServer(port = 3007): Promise<MemoryMCPServer> {
  const server = new MemoryMCPServer();
  await server.start(port);
  return server;
}

// Start server if this file is run directly
if (require.main === module) {
  startServer().catch(console.error);
}
