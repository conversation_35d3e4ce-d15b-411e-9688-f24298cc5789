from fastapi import FastAP<PERSON>, Query, HTTPException
from google_maps import GoogleMapsMCP

app = FastAPI()
mcp = GoogleMapsMCP(api_key="YOUR_GOOGLE_MAPS_API_KEY")

@app.get("/geocode")
def geocode(address: str):
    try:
        return mcp.geocode_address(address)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/nearby")
def nearby(lat: float, lng: float, radius: int, keyword: str = ""):
    try:
        return mcp.search_nearby(lat, lng, radius, keyword)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
