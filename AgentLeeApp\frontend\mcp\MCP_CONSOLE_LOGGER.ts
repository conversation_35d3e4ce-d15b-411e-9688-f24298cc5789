import { MCPServer } from 'model-context-protocol';

export async function startServer(port = 3025) {
    const server = new MCPServer({ port });
    server.registerTool('log', {
        title: 'Console Logger',
        parameters: { type: 'object', properties: { message: { type: 'string' } }, required: ['message'] },
        returns: { type: 'object', properties: { success: { type: 'boolean' } } }
    }, async ({ message }) => {
        console.log(`[MCP_LOG] ${message}`);
        return { success: true };
    });
    await server.listen();
}