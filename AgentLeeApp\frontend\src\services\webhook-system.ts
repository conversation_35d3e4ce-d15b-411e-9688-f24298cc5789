/**
 * MCP Webhook System
 * Handles incoming webhooks from external services and triggers MCP actions
 */

import express, { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';
import { EventEmitter } from 'events';
import axios from 'axios';

interface WebhookConfig {
  port: number;
  enableSecurity: boolean;
  secrets: Record<string, string>; // service -> secret
  enableLogging: boolean;
  retryAttempts: number;
  timeout: number;
}

interface WebhookPayload {
  service: string;
  event: string;
  data: any;
  timestamp: Date;
  signature?: string;
  headers: Record<string, string>;
}

interface WebhookHandler {
  service: string;
  event: string;
  handler: (payload: WebhookPayload) => Promise<any>;
  description?: string;
}

interface MCPAction {
  tool: string;
  service?: string;
  params: any;
  condition?: (payload: WebhookPayload) => boolean;
}

class MCPWebhookSystem extends EventEmitter {
  private app: express.Application;
  private config: WebhookConfig;
  private handlers: Map<string, WebhookHandler[]> = new Map();
  private mcpActions: Map<string, MCPAction[]> = new Map();
  private mcpGatewayUrl: string;

  constructor(config: Partial<WebhookConfig> = {}, mcpGatewayUrl: string = 'http://localhost:3000') {
    super();
    
    this.config = {
      port: config.port || 3001,
      enableSecurity: config.enableSecurity !== false,
      secrets: config.secrets || {},
      enableLogging: config.enableLogging !== false,
      retryAttempts: config.retryAttempts || 3,
      timeout: config.timeout || 30000
    };

    this.mcpGatewayUrl = mcpGatewayUrl;
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
    this.setupDefaultHandlers();
  }

  private setupMiddleware() {
    // Raw body parser for signature verification
    this.app.use('/webhook', express.raw({ type: 'application/json', limit: '10mb' }));
    
    // JSON parser for other routes
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));

    // Logging middleware
    if (this.config.enableLogging) {
      this.app.use((req: Request, res: Response, next: NextFunction) => {
        const start = Date.now();
        res.on('finish', () => {
          const duration = Date.now() - start;
          console.log(`${req.method} ${req.path} - ${res.statusCode} (${duration}ms)`);
        });
        next();
      });
    }
  }

  private setupRoutes() {
    // Health check
    this.app.get('/health', (req: Request, res: Response) => {
      res.json({
        status: 'ok',
        service: 'MCP Webhook System',
        timestamp: new Date().toISOString(),
        handlers: this.getHandlerSummary(),
        actions: this.getActionSummary()
      });
    });

    // Generic webhook endpoint
    this.app.post('/webhook/:service', async (req: Request, res: Response) => {
      try {
        const service = req.params.service;
        const rawBody = req.body;
        
        // Verify signature if security is enabled
        if (this.config.enableSecurity) {
          const isValid = this.verifySignature(service, rawBody, req.headers);
          if (!isValid) {
            return res.status(401).json({ error: 'Invalid signature' });
          }
        }

        // Parse payload
        const payload: WebhookPayload = {
          service,
          event: this.extractEvent(service, req.headers, JSON.parse(rawBody.toString())),
          data: JSON.parse(rawBody.toString()),
          timestamp: new Date(),
          signature: req.headers['x-hub-signature-256'] as string || req.headers['x-signature'] as string,
          headers: req.headers as Record<string, string>
        };

        // Process webhook
        await this.processWebhook(payload);
        
        res.json({ success: true, message: 'Webhook processed successfully' });
      } catch (error) {
        console.error('Webhook processing error:', error);
        res.status(500).json({ 
          error: 'Webhook processing failed',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    });

    // List handlers endpoint
    this.app.get('/handlers', (req: Request, res: Response) => {
      res.json({
        handlers: this.getHandlerSummary(),
        actions: this.getActionSummary()
      });
    });

    // Manual trigger endpoint (for testing)
    this.app.post('/trigger/:service/:event', async (req: Request, res: Response) => {
      try {
        const { service, event } = req.params;
        const payload: WebhookPayload = {
          service,
          event,
          data: req.body,
          timestamp: new Date(),
          headers: req.headers as Record<string, string>
        };

        await this.processWebhook(payload);
        res.json({ success: true, message: 'Event triggered successfully' });
      } catch (error) {
        res.status(500).json({ 
          error: 'Event trigger failed',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    });
  }

  private setupDefaultHandlers() {
    // GitHub webhook handlers
    this.addHandler({
      service: 'github',
      event: 'push',
      handler: async (payload) => {
        const { commits, repository } = payload.data;
        console.log(`📝 GitHub push to ${repository.name}: ${commits.length} commits`);
        
        // Trigger TTS for commit messages
        for (const commit of commits) {
          await this.executeMCPAction('sendTTSMessage', {
            text: `New commit: ${commit.message}`,
            voice: 'default'
          });
        }
        
        return { processed: commits.length };
      },
      description: 'Process GitHub push events and generate TTS for commits'
    });

    this.addHandler({
      service: 'github',
      event: 'issues',
      handler: async (payload) => {
        const { action, issue } = payload.data;
        console.log(`🐛 GitHub issue ${action}: ${issue.title}`);
        
        if (action === 'opened') {
          // Take screenshot of issue URL and send notification
          await this.executeMCPAction('screenshot', {
            url: issue.html_url,
            width: 1280,
            height: 720
          });
          
          await this.executeMCPAction('sendMessage', {
            chatId: process.env.PERSONAL_TELEGRAM_CHAT_ID,
            text: `New issue opened: ${issue.title}\n${issue.html_url}`
          });
        }
        
        return { action, issue: issue.title };
      },
      description: 'Process GitHub issue events'
    });

    // Discord webhook handlers
    this.addHandler({
      service: 'discord',
      event: 'message',
      handler: async (payload) => {
        const { content, author } = payload.data;
        console.log(`💬 Discord message from ${author.username}: ${content}`);
        
        // Process commands
        if (content.startsWith('!screenshot')) {
          const url = content.split(' ')[1];
          if (url) {
            const result = await this.executeMCPAction('screenshot', { url });
            return { screenshot: result };
          }
        }
        
        return { processed: true };
      },
      description: 'Process Discord messages and commands'
    });

    // Telegram webhook handlers
    this.addHandler({
      service: 'telegram',
      event: 'message',
      handler: async (payload) => {
        const { message } = payload.data;
        console.log(`📱 Telegram message: ${message.text}`);
        
        // Process bot commands
        if (message.text?.startsWith('/')) {
          const command = message.text.split(' ')[0];
          const args = message.text.split(' ').slice(1);
          
          switch (command) {
            case '/screenshot':
              if (args[0]) {
                const result = await this.executeMCPAction('screenshot', { url: args[0] });
                return { command, result };
              }
              break;
            case '/search':
              if (args.length > 0) {
                const result = await this.executeMCPAction('webSearch', { 
                  query: args.join(' ') 
                });
                return { command, result };
              }
              break;
          }
        }
        
        return { processed: true };
      },
      description: 'Process Telegram bot commands'
    });
  }

  private verifySignature(service: string, body: Buffer, headers: any): boolean {
    const secret = this.config.secrets[service];
    if (!secret) {
      console.warn(`No secret configured for service: ${service}`);
      return false;
    }

    let signature: string | undefined;
    let expectedSignature: string;

    switch (service) {
      case 'github':
        signature = headers['x-hub-signature-256'];
        expectedSignature = 'sha256=' + crypto
          .createHmac('sha256', secret)
          .update(body)
          .digest('hex');
        break;
      
      case 'discord':
        signature = headers['x-signature-ed25519'];
        // Discord uses Ed25519 signatures - simplified for example
        expectedSignature = signature || '';
        break;
      
      default:
        signature = headers['x-signature'];
        expectedSignature = crypto
          .createHmac('sha256', secret)
          .update(body)
          .digest('hex');
    }

    return signature === expectedSignature;
  }

  private extractEvent(service: string, headers: any, data: any): string {
    switch (service) {
      case 'github':
        return headers['x-github-event'] || 'unknown';
      case 'discord':
        return data.type || 'message';
      case 'telegram':
        return data.message ? 'message' : 'unknown';
      default:
        return headers['x-event-type'] || data.event || 'unknown';
    }
  }

  private async processWebhook(payload: WebhookPayload): Promise<void> {
    this.emit('webhookReceived', payload);

    // Find and execute handlers
    const serviceHandlers = this.handlers.get(payload.service) || [];
    const matchingHandlers = serviceHandlers.filter(h => h.event === payload.event);

    if (matchingHandlers.length === 0) {
      console.warn(`No handlers found for ${payload.service}:${payload.event}`);
      return;
    }

    // Execute handlers in parallel
    const handlerPromises = matchingHandlers.map(async (handler) => {
      try {
        const result = await handler.handler(payload);
        this.emit('handlerSuccess', { handler: handler.service + ':' + handler.event, result });
        return result;
      } catch (error) {
        this.emit('handlerError', { handler: handler.service + ':' + handler.event, error });
        throw error;
      }
    });

    await Promise.allSettled(handlerPromises);

    // Execute MCP actions
    const actions = this.mcpActions.get(`${payload.service}:${payload.event}`) || [];
    for (const action of actions) {
      if (!action.condition || action.condition(payload)) {
        await this.executeMCPAction(action.tool, action.params, action.service);
      }
    }
  }

  private async executeMCPAction(tool: string, params: any, service?: string): Promise<any> {
    try {
      const response = await axios.post(
        `${this.mcpGatewayUrl}/api/tools/execute`,
        { tool, service, ...params },
        { timeout: this.config.timeout }
      );
      
      return response.data;
    } catch (error) {
      console.error(`MCP action failed: ${tool}`, error);
      throw error;
    }
  }

  // Public API methods
  addHandler(handler: WebhookHandler): void {
    const key = handler.service;
    const handlers = this.handlers.get(key) || [];
    handlers.push(handler);
    this.handlers.set(key, handlers);
    
    console.log(`Added webhook handler: ${handler.service}:${handler.event}`);
  }

  removeHandler(service: string, event: string): boolean {
    const handlers = this.handlers.get(service) || [];
    const index = handlers.findIndex(h => h.event === event);
    
    if (index !== -1) {
      handlers.splice(index, 1);
      this.handlers.set(service, handlers);
      console.log(`Removed webhook handler: ${service}:${event}`);
      return true;
    }
    
    return false;
  }

  addMCPAction(service: string, event: string, action: MCPAction): void {
    const key = `${service}:${event}`;
    const actions = this.mcpActions.get(key) || [];
    actions.push(action);
    this.mcpActions.set(key, actions);
    
    console.log(`Added MCP action: ${key} -> ${action.tool}`);
  }

  private getHandlerSummary(): any {
    const summary: any = {};
    this.handlers.forEach((handlers, service) => {
      summary[service] = handlers.map(h => ({
        event: h.event,
        description: h.description
      }));
    });
    return summary;
  }

  private getActionSummary(): any {
    const summary: any = {};
    this.mcpActions.forEach((actions, key) => {
      summary[key] = actions.map(a => ({
        tool: a.tool,
        service: a.service,
        hasCondition: !!a.condition
      }));
    });
    return summary;
  }

  async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.app.listen(this.config.port, (err?: Error) => {
        if (err) {
          reject(err);
          return;
        }

        console.log(`🪝 MCP Webhook System listening on port ${this.config.port}`);
        console.log(`🔒 Security: ${this.config.enableSecurity ? 'enabled' : 'disabled'}`);
        console.log(`📝 Logging: ${this.config.enableLogging ? 'enabled' : 'disabled'}`);
        
        this.emit('started', { port: this.config.port });
        resolve();
      });
    });
  }

  async stop(): Promise<void> {
    this.emit('stopped');
    console.log('🪝 Webhook system stopped');
  }
}

export { MCPWebhookSystem };
export type { WebhookConfig, WebhookPayload, WebhookHandler, MCPAction };

// Start webhook system if this file is run directly
if (require.main === module) {
  const webhookSystem = new MCPWebhookSystem({
    secrets: {
      github: process.env.GITHUB_WEBHOOK_SECRET || 'your-github-secret',
      discord: process.env.DISCORD_WEBHOOK_SECRET || 'your-discord-secret',
      telegram: process.env.TELEGRAM_WEBHOOK_SECRET || 'your-telegram-secret'
    }
  });
  
  webhookSystem.on('webhookReceived', (payload) => {
    console.log('📨 Webhook received:', payload.service, payload.event);
  });
  
  webhookSystem.start().catch(console.error);
  
  // Graceful shutdown
  process.on('SIGTERM', () => webhookSystem.stop());
  process.on('SIGINT', () => webhookSystem.stop());
}
