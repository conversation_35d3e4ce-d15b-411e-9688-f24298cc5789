import cv2

class CameraVisionMCP:
    def __init__(self, camera_index=0):
        self.cap = cv2.VideoCapture(camera_index)

    def capture_frame(self, save_path="frame.jpg"):
        if not self.cap.isOpened():
            return {"error": "Camera not accessible."}
        ret, frame = self.cap.read()
        if ret:
            cv2.imwrite(save_path, frame)
            return {"status": "captured", "path": save_path}
        return {"error": "Failed to capture frame."}

    def release(self):
        self.cap.release()
