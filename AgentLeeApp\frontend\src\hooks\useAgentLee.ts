import { useState, useCallback } from 'react';

export interface Message {
  id: string;
  sender: 'user' | 'agent';
  text: string;
}

export function useAgentLee() {
  const [messages, setMessages] = useState<Message[]>([]);

  // Simulate sending a message to <PERSON> and getting a response
  const sendMessage = useCallback(async (query: string) => {
    const userMessage: Message = {
      id: `${Date.now()}-user`,
      sender: 'user',
      text: query,
    };
    setMessages(prev => [...prev, userMessage]);

    // Simulate agent response after a short delay
    await new Promise(res => setTimeout(res, 1200));
    const agentMessage: Message = {
      id: `${Date.now()}-agent`,
      sender: 'agent',
      text: `<PERSON>'s response to: "${query}"`,
    };
    setMessages(prev => [...prev, agentMessage]);
  }, []);

  return {
    messages,
    sendMessage,
  };
}
