import { MCPServer } from 'model-context-protocol';
import { google } from 'googleapis';

export async function startServer(port = 3021, auth: any) {
    const calendar = google.calendar({ version: 'v3', auth });
    const server = new MCPServer({ port });
    server.registerTool('listEvents', {
        title: 'List Calendar Events',
        parameters: {
            type: 'object',
            properties: { calendarId: { type: 'string', default: 'primary' } }
        },
        returns: {
            type: 'object',
            properties: {
                events: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            summary: { type: 'string' },
                            start: { type: 'string' },
                            end: { type: 'string' }
                        }
                    }
                }
            }
        }
    }, async ({ calendarId }) => {
        const resp = await calendar.events.list({ calendarId, timeMin: (new Date()).toISOString() });
        const events = resp.data.items?.map(e => ({
            id: e.id!,
            summary: e.summary || '',
            start: e.start?.dateTime || '',
            end: e.end?.dateTime || ''
        })) || [];
        return { events };
    });
    await server.listen();
}