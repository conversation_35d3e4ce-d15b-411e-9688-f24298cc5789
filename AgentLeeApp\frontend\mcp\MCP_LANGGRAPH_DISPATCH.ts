import { MCPServer } from 'model-context-protocol';
import axios from 'axios';

export async function startServer(port = 3033, endpoint: string) {
    const server = new MCPServer({ port });
    server.registerTool('dispatchLangGraph', {
        title: 'LangGraph Dispatcher',
        parameters: {
            type: 'object',
            properties: { pipelineId: { type: 'string' }, inputs: { type: 'object' } },
            required: ['pipelineId', 'inputs']
        },
        returns: { type: 'object', properties: { output: { type: 'object' } } }
    }, async ({ pipelineId, inputs }) => {
        const resp = await axios.post(`${endpoint}/run/${pipelineId}`, inputs);
        return { output: resp.data };
    });
    await server.listen();
}