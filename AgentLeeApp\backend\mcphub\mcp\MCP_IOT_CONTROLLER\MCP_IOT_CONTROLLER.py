import paho.mqtt.client as mqtt

class IoTControllerMCP:
    def __init__(self, broker_url="localhost", broker_port=1883):
        self.broker_url = broker_url
        self.broker_port = broker_port
        self.client = mqtt.Client()

    def connect(self):
        self.client.connect(self.broker_url, self.broker_port)
        self.client.loop_start()
        return {"status": "connected", "broker": self.broker_url, "port": self.broker_port}

    def publish(self, topic: str, payload: str):
        result = self.client.publish(topic, payload)
        return {
            "status": "published",
            "topic": topic,
            "payload": payload,
            "result": result.rc
        }

    def disconnect(self):
        self.client.loop_stop()
        self.client.disconnect()
        return {"status": "disconnected"}
