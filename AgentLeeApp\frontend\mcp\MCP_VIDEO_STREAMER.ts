
/**
 * Video Streamer MCP Server
 * Provides video streaming and processing capabilities
 */

import { MCPServer, MCPTool, createToolSchema, ToolPropertyTypes } from '../mcp-base';
import fs from 'fs';
import path from 'path';

class VideoStreamerMCPServer extends MCPServer {
  constructor() {
    super({
      name: 'Video Streamer MCP Server',
      version: '1.0.0',
      port: 3004
    });

    this.setupTools();
  }

  private setupTools(): void {
    // Stream Video Tool
    const streamVideoTool: MCPTool = {
      name: 'streamVideo',
      description: 'Stream video by URL or file path',
      inputSchema: createToolSchema({
        url: ToolPropertyTypes.string('Video URL to stream', ''),
        filePath: ToolPropertyTypes.string('Local file path to stream', ''),
        format: ToolPropertyTypes.string('Output format (base64, info)', 'base64'),
        maxSize: ToolPropertyTypes.number('Maximum file size in MB', 1, 100)
      }),
      handler: async ({ url, filePath, format = 'base64', maxSize = 10 }: {
        url?: string;
        filePath?: string;
        format?: string;
        maxSize?: number;
      }) => {
        try {
          if (!url && !filePath) {
            return {
              success: false,
              error: 'Either url or filePath must be provided'
            };
          }

          let buffer: Buffer;
          let metadata: any = {};

          if (filePath) {
            // Handle local file
            if (!fs.existsSync(filePath)) {
              return {
                success: false,
                error: 'File not found'
              };
            }

            const stats = fs.statSync(filePath);
            const fileSizeMB = stats.size / (1024 * 1024);

            if (fileSizeMB > maxSize) {
              return {
                success: false,
                error: `File size (${fileSizeMB.toFixed(2)}MB) exceeds maximum allowed size (${maxSize}MB)`
              };
            }

            buffer = fs.readFileSync(filePath);
            metadata = {
              fileName: path.basename(filePath),
              fileSize: stats.size,
              fileSizeMB: fileSizeMB.toFixed(2),
              extension: path.extname(filePath)
            };
          } else if (url) {
            // Handle URL - for now, just return info about the URL
            // In a real implementation, you would fetch the video
            return {
              success: true,
              message: 'URL video streaming not implemented in mock version',
              url,
              format
            };
          }

          if (format === 'info') {
            return {
              success: true,
              metadata,
              format: 'info'
            };
          }

          // Return base64 encoded video
          const video_base64 = buffer!.toString('base64');

          return {
            success: true,
            video_base64,
            metadata,
            format,
            size: buffer!.length
          };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          return {
            success: false,
            error: errorMessage
          };
        }
      }
    };

    // Get Video Info Tool
    const getVideoInfoTool: MCPTool = {
      name: 'getVideoInfo',
      description: 'Get information about a video file',
      inputSchema: createToolSchema({
        filePath: ToolPropertyTypes.string('Path to video file')
      }, ['filePath']),
      handler: async ({ filePath }: { filePath: string }) => {
        try {
          if (!fs.existsSync(filePath)) {
            return {
              success: false,
              error: 'File not found'
            };
          }

          const stats = fs.statSync(filePath);
          const fileSizeMB = stats.size / (1024 * 1024);

          return {
            success: true,
            info: {
              fileName: path.basename(filePath),
              fileSize: stats.size,
              fileSizeMB: fileSizeMB.toFixed(2),
              extension: path.extname(filePath),
              created: stats.birthtime,
              modified: stats.mtime
            }
          };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          return {
            success: false,
            error: errorMessage
          };
        }
      }
    };

    // Add all tools
    this.addTool(streamVideoTool);
    this.addTool(getVideoInfoTool);
  }

  protected override getMetrics(): any {
    const baseMetrics = super.getMetrics();
    return {
      ...baseMetrics,
      supportedFormats: ['mp4', 'avi', 'mov', 'mkv'],
      maxFileSize: '100MB'
    };
  }
}

// Export for use in other modules
export async function startServer(port = 3004): Promise<VideoStreamerMCPServer> {
  const server = new VideoStreamerMCPServer();
  await server.start(port);
  return server;
}

// Start server if this file is run directly
if (require.main === module) {
  startServer().catch(console.error);
}
