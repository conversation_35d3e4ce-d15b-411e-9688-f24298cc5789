from fastapi import Fast<PERSON><PERSON>, HTTPException, Body
from MCP_GITHUB_PULL_REQUEST import GitH<PERSON><PERSON>MCP

# Replace with your personal GitHub token
mcp = GitHubPRMCP(token="YOUR_GITHUB_TOKEN")

app = FastAPI()

@app.post("/github/pr/create")
def create_pr(
    owner: str = Body(...),
    repo: str = Body(...),
    title: str = Body(...),
    body: str = Body(""),
    head: str = Body(...),
    base: str = Body("main")
):
    try:
        return mcp.create_pull_request(owner, repo, title, body, head, base)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/github/pr/list")
def list_prs(owner: str, repo: str, state: str = "open"):
    try:
        return mcp.list_pull_requests(owner, repo, state)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
