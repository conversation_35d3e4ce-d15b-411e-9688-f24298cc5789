import { MCPServer } from 'model-context-protocol';
import { Camera } from 'node-webcam';

export async function startServer(port = 3022, options: any = {}) {
    const server = new MCPServer({ port });
    const cam = Camera.create({ ...options });
    server.registerTool('captureImage', {
        title: 'Camera Vision',
        parameters: {
            type: 'object',
            properties: { delay: { type: 'integer', default: 0 } }
        },
        returns: {
            type: 'object',
            properties: { image_base64: { type: 'string' } }
        }
    }, async ({ delay }) => {
        const file = await new Promise<string>((res, rej) => {
            cam.capture('temp', { delay }, (err, data) => err ? rej(err) : res(data));
        });
        const buffer = fs.readFileSync(file);
        return { image_base64: buffer.toString('base64') };
    });
    await server.listen();
}