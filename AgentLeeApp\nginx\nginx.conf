# Nginx configuration for MCP CDN and reverse proxy
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging format
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';

    access_log /var/log/nginx/access.log main;

    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=webhook:10m rate=5r/s;

    # Upstream servers
    upstream mcp_gateway {
        least_conn;
        server mcp-gateway:3000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream mcp_webhooks {
        least_conn;
        server mcp-webhooks:3001 max_fails=3 fail_timeout=30s;
        keepalive 16;
    }

    upstream mcp_hub {
        least_conn;
        server mcp-hub:3002 max_fails=3 fail_timeout=30s;
        keepalive 16;
    }

    # Cache zones
    proxy_cache_path /var/cache/nginx/api levels=1:2 keys_zone=api_cache:10m max_size=100m inactive=60m use_temp_path=off;
    proxy_cache_path /var/cache/nginx/static levels=1:2 keys_zone=static_cache:10m max_size=500m inactive=24h use_temp_path=off;

    # Main server block
    server {
        listen 80;
        server_name localhost agentlee.fly.dev *.agentlee.fly.dev;

        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # MCP Gateway API routes with caching
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            
            # Cache configuration
            proxy_cache api_cache;
            proxy_cache_valid 200 302 5m;
            proxy_cache_valid 404 1m;
            proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
            proxy_cache_lock on;
            proxy_cache_lock_timeout 5s;
            
            # Cache bypass for certain endpoints
            proxy_cache_bypass $http_cache_control;
            proxy_no_cache $http_cache_control;
            
            # Skip cache for POST requests and authenticated requests
            set $skip_cache 0;
            if ($request_method = POST) {
                set $skip_cache 1;
            }
            if ($http_authorization) {
                set $skip_cache 1;
            }
            proxy_cache_bypass $skip_cache;
            proxy_no_cache $skip_cache;

            # Proxy settings
            proxy_pass http://mcp_gateway;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            
            # Timeouts
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
            
            # Buffer settings
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
            proxy_busy_buffers_size 8k;
            
            # Add cache status header
            add_header X-Cache-Status $upstream_cache_status;
        }

        # Webhook endpoints
        location /webhook/ {
            limit_req zone=webhook burst=10 nodelay;
            
            proxy_pass http://mcp_webhooks;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Increase body size for webhook payloads
            client_max_body_size 10m;
            
            # Timeouts
            proxy_connect_timeout 10s;
            proxy_send_timeout 10s;
            proxy_read_timeout 10s;
        }

        # Direct MCP Hub access (for debugging)
        location /mcp/ {
            limit_req zone=api burst=10 nodelay;
            
            proxy_pass http://mcp_hub/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Static file serving with aggressive caching
        location /static/ {
            alias /var/www/static/;
            
            # Cache configuration
            proxy_cache static_cache;
            proxy_cache_valid 200 24h;
            proxy_cache_valid 404 1m;
            
            # Browser caching
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header X-Cache-Status $upstream_cache_status;
            
            # Compression
            gzip_static on;
            
            # Security
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
                access_log off;
            }
        }

        # Screenshots and generated content
        location /screenshots/ {
            alias /var/www/screenshots/;
            
            # Cache for 1 hour
            expires 1h;
            add_header Cache-Control "public";
            
            # Security - prevent direct access to sensitive files
            location ~* \.(php|pl|py|jsp|asp|sh|cgi)$ {
                deny all;
            }
        }

        # Monitoring endpoints
        location /metrics {
            proxy_pass http://mcp_gateway/metrics;
            allow 127.0.0.1;
            allow **********/16;  # Docker network
            deny all;
        }

        # WebSocket support for real-time features
        location /ws {
            proxy_pass http://mcp_gateway;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket specific timeouts
            proxy_read_timeout 86400s;
            proxy_send_timeout 86400s;
        }

        # Error pages
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /404.html {
            root /var/www/error;
            internal;
        }
        
        location = /50x.html {
            root /var/www/error;
            internal;
        }
    }

    # HTTPS server (if SSL certificates are available)
    server {
        listen 443 ssl http2;
        server_name agentlee.fly.dev *.agentlee.fly.dev;

        # SSL configuration
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_session_timeout 1d;
        ssl_session_cache shared:SSL:50m;
        ssl_session_tickets off;

        # Modern configuration
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;

        # HSTS
        add_header Strict-Transport-Security "max-age=63072000" always;

        # Include all the same location blocks as HTTP server
        include /etc/nginx/conf.d/locations.conf;
    }

    # Redirect HTTP to HTTPS
    server {
        listen 80;
        server_name agentlee.fly.dev *.agentlee.fly.dev;
        return 301 https://$server_name$request_uri;
    }
}

# Stream block for TCP/UDP load balancing (if needed)
stream {
    # Example: Load balance database connections
    upstream postgres_backend {
        server postgres:5432;
    }
    
    server {
        listen 5433;
        proxy_pass postgres_backend;
        proxy_timeout 1s;
        proxy_responses 1;
    }
}
