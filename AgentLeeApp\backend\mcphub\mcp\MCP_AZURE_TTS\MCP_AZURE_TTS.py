import requests
import uuid
import xml.etree.ElementTree as ET

class AzureTTSMCP:
    def __init__(self, subscription_key: str, region: str):
        self.subscription_key = subscription_key
        self.region = region
        self.endpoint = f"https://{region}.tts.speech.microsoft.com/cognitiveservices/v1"
        self.headers = {
            "Ocp-Apim-Subscription-Key": subscription_key,
            "Content-Type": "application/ssml+xml",
            "X-Microsoft-OutputFormat": "audio-16khz-32kbitrate-mono-mp3",
            "User-Agent": "AgentLeeTTS"
        }

    def generate_ssml(self, text: str, voice: str = "en-US-JennyNeural"):
        speak = ET.Element("speak", version="1.0", xmlns="http://www.w3.org/2001/10/synthesis", lang="en-US")
        voice_element = ET.SubElement(speak, "voice", name=voice)
        voice_element.text = text
        return ET.tostring(speak, encoding="unicode")

    def synthesize(self, text: str, voice: str = "en-US-JennyNeural", filename="output.mp3"):
        ssml = self.generate_ssml(text, voice)
        response = requests.post(self.endpoint, headers=self.headers, data=ssml)
        if response.status_code == 200:
            with open(filename, "wb") as f:
                f.write(response.content)
            return {"status": "success", "path": filename}
        else:
            return {"error": response.text, "code": response.status_code}
