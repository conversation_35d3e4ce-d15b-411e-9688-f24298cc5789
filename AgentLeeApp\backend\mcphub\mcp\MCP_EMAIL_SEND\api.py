from fastapi import FastAP<PERSON>, HTTPException, Body
from MCP_EMAIL_SEND import EmailSendMCP

# Replace with real credentials (or inject from env file)
mcp = EmailSendMCP(
    smtp_server="smtp.gmail.com",
    smtp_port=587,
    username="<EMAIL>",
    password="yourpassword"
)

app = FastAPI()

@app.post("/email/send")
def send_email(
    to_email: str = Body(...),
    subject: str = Body(...),
    body: str = Body(...)
):
    try:
        return mcp.send_email(to_email, subject, body)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
