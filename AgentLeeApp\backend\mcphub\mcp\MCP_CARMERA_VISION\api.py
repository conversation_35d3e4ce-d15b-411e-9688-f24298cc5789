from fastapi import FastAP<PERSON>, HTTPException
from MCP_CAMERA_VISION import CameraVisionMCP

app = FastAPI()
mcp = CameraVisionMCP()

@app.get("/camera/capture")
def capture_frame():
    try:
        return mcp.capture_frame()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/camera/release")
def release_camera():
    try:
        mcp.release()
        return {"status": "camera released"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
