import requests
from bs4 import BeautifulSoup

class WebSearchMCP:
    def __init__(self):
        self.duckduckgo_html = "https://html.duckduckgo.com/html/"

    def search(self, query: str, max_results: int = 5):
        params = {"q": query}
        headers = {"User-Agent": "AgentLeeBot/1.0"}
        response = requests.post(self.duckduckgo_html, data=params, headers=headers)
        soup = BeautifulSoup(response.text, "html.parser")

        results = []
        for result in soup.find_all("a", class_="result__a")[:max_results]:
            title = result.get_text()
            href = result.get("href")
            results.append({"title": title, "url": href})
        return results
