from fastapi import FastAP<PERSON>, HTTPException, Query
from MCP_SIMPLE_TEST import SimpleTestMCP

mcp = SimpleTestMCP()
app = FastAPI()

@app.get("/test/ping")
def ping():
    try:
        return mcp.ping()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/test/echo")
def echo(text: str = Query(...)):
    try:
        return mcp.echo(text)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
