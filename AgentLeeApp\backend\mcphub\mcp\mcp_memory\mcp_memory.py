import os
import json
from datetime import datetime

class MemoryMCP:
    def __init__(self, storage_dir="memory_store"):
        os.makedirs(storage_dir, exist_ok=True)
        self.storage_dir = storage_dir

    def _filepath(self, user_id: str):
        return os.path.join(self.storage_dir, f"{user_id}.json")

    def store(self, user_id: str, key: str, value):
        path = self._filepath(user_id)
        data = {}
        if os.path.exists(path):
            with open(path, "r") as f:
                data = json.load(f)
        timestamp = datetime.utcnow().isoformat()
        data[key] = {"value": value, "timestamp": timestamp}
        with open(path, "w") as f:
            json.dump(data, f, indent=2)
        return {"status": "stored", "user": user_id, "key": key}

    def recall(self, user_id: str, key: str):
        path = self._filepath(user_id)
        if not os.path.exists(path):
            return {"error": "No memory file found"}
        with open(path, "r") as f:
            data = json.load(f)
        return data.get(key, {"error": "Key not found"})

    def dump(self, user_id: str):
        path = self._filepath(user_id)
        if not os.path.exists(path):
            return {"error": "No memory file found"}
        with open(path, "r") as f:
            return json.load(f)
