import requests

class HomeAssistantMCP:
    def __init__(self, base_url: str, token: str):
        self.base_url = base_url.rstrip("/")
        self.headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }

    def list_entities(self):
        url = f"{self.base_url}/api/states"
        response = requests.get(url, headers=self.headers)
        response.raise_for_status()
        return response.json()

    def call_service(self, domain: str, service: str, payload: dict):
        url = f"{self.base_url}/api/services/{domain}/{service}"
        response = requests.post(url, headers=self.headers, json=payload)
        response.raise_for_status()
        return response.json()
