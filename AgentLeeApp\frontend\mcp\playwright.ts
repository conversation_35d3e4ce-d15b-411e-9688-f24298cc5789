/**
 * Playwright MCP Server with IoT Integration
 * Enables advanced browser automation triggered by IoT events
 */

import { MCPServer, MCPTool, createToolSchema, ToolPropertyTypes } from '../mcp-base';
import axios from 'axios';

class PlaywrightIoTMCPServer extends MCPServer {
  private iotControllerUrl: string;

  constructor() {
    super({
      name: 'Playwright IoT MCP Server',
      version: '1.0.0',
      port: 3016
    });

    this.iotControllerUrl = 'http://localhost:3010';
    this.setupTools();
  }

  private setupTools(): void {
    // IoT-Triggered Playwright Action Tool
    const iotPlaywrightActionTool: MCPTool = {
      name: 'iotPlaywrightAction',
      description: 'Execute Playwright automation based on IoT device trigger',
      inputSchema: createToolSchema({
        deviceId: ToolPropertyTypes.string('IoT device ID that triggered the action'),
        action: ToolPropertyTypes.string('Playwright action to execute'),
        url: ToolPropertyTypes.string('URL to navigate to'),
        selector: ToolPropertyTypes.string('CSS selector for element interaction'),
        text: ToolPropertyTypes.string('Text to input or search for')
      }, ['deviceId', 'action']),
      handler: async (params: any) => {
        return await this.executeIoTPlaywrightAction(params);
      }
    };

    // Process Voice Command Tool
    const processVoiceCommandTool: MCPTool = {
      name: 'processVoiceCommand',
      description: 'Process voice commands for Playwright automation with IoT context',
      inputSchema: createToolSchema({
        command: ToolPropertyTypes.string('Natural language command'),
        speaker: ToolPropertyTypes.string('Speaker/user identifier'),
        iotContext: ToolPropertyTypes.object('Current IoT device states', {})
      }, ['command']),
      handler: async (params: any) => {
        return await this.processVoiceCommand(params.command, params.speaker, params.iotContext);
      }
    };

    this.addTool(iotPlaywrightActionTool);
    this.addTool(processVoiceCommandTool);
  }

  private async executeIoTPlaywrightAction(params: any): Promise<any> {
    const { deviceId, action, url, selector, text } = params;

    try {
      // Get device status from IoT controller
      const deviceResponse = await axios.get(`${this.iotControllerUrl}/devices/${deviceId}`);
      const device = deviceResponse.data;

      // Simulate Playwright action execution
      let result: any;
      switch (action) {
        case 'navigate':
          result = { action: 'navigated', url, success: true };
          break;
        case 'click':
          result = { action: 'clicked', selector, success: true };
          break;
        case 'type':
          result = { action: 'typed', selector, text, success: true };
          break;
        case 'screenshot':
          result = { action: 'screenshot_taken', success: true };
          break;
        default:
          throw new Error(`Unsupported Playwright action: ${action}`);
      }

      return {
        success: true,
        deviceId,
        deviceName: device.name,
        playwrightAction: action,
        result,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        success: false,
        error: `IoT Playwright action failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        deviceId,
        action
      };
    }
  }

  private async processVoiceCommand(command: string, speaker?: string, iotContext?: any): Promise<any> {
    const lowerCommand = command.toLowerCase();

    // Playwright command patterns with IoT context
    if (lowerCommand.includes('automate') && lowerCommand.includes('when')) {
      return {
        success: true,
        message: 'Playwright IoT automation command processed',
        command,
        speaker,
        action: 'automation_setup'
      };
    }

    return {
      success: false,
      error: 'Could not understand Playwright command',
      command,
      suggestion: 'Try commands like "automate login when motion detected"'
    };
  }
}

// Export for use as module
export default PlaywrightIoTMCPServer;

// CLI support
if (require.main === module) {
  const server = new PlaywrightIoTMCPServer();
  server.start().then(() => {
    console.log('🧪 Playwright IoT MCP Server started');
  }).catch(console.error);
}
