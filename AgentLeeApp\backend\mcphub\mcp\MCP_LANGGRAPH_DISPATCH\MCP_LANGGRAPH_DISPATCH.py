class LangGraphDispatchMCP:
    def __init__(self):
        self.registry = {
            "summarize": "GEMINI",
            "plan": "PHI-3",
            "validate": "QWEN",
            "reason": "AZR",
            "longform": "LLAMA",
            "monitor": "ECHO"
        }

    def dispatch_task(self, instruction: str):
        instruction = instruction.lower()
        for keyword, agent in self.registry.items():
            if keyword in instruction:
                return {"instruction": instruction, "agent": agent}
        return {"instruction": instruction, "agent": "UNKNOWN", "note": "No match found"}

    def all_routes(self):
        return self.registry
