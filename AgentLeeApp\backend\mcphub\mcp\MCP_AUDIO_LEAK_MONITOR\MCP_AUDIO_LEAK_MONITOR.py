import sounddevice as sd
import numpy as np

class AudioLeakMonitorMCP:
    def __init__(self, threshold_db=-30, duration=3, samplerate=44100):
        self.threshold_db = threshold_db
        self.duration = duration
        self.samplerate = samplerate

    def detect_leak(self):
        try:
            recording = sd.rec(int(self.duration * self.samplerate), samplerate=self.samplerate, channels=1, dtype='float64')
            sd.wait()

            rms = np.sqrt(np.mean(recording**2))
            db = 20 * np.log10(rms) if rms > 0 else -100

            leak_detected = db > self.threshold_db
            return {
                "rms": round(rms, 5),
                "db": round(db, 2),
                "leak_detected": leak_detected,
                "threshold_db": self.threshold_db
            }
        except Exception as e:
            return {"error": str(e)}
