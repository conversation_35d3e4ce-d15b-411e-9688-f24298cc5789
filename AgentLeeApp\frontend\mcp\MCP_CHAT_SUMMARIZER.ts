import { MCPServer } from 'model-context-protocol';
import axios from 'axios';

export async function startServer(port = 3023, aiEndpoint: string) {
    const server = new MCPServer({ port });
    server.registerTool('summarizeChat', {
        title: 'Chat Summarizer',
        parameters: {
            type: 'object',
            properties: {
                messages: { type: 'array', items: { type: 'object', properties: { role: { type: 'string' }, content: { type: 'string' } } } }
            },
            required: ['messages']
        },
        returns: {
            type: 'object',
            properties: { summary: { type: 'string' } }
        }
    }, async ({ messages }) => {
        const resp = await axios.post(aiEndpoint, { messages });
        return { summary: resp.data.summary };
    });
    await server.listen();
}