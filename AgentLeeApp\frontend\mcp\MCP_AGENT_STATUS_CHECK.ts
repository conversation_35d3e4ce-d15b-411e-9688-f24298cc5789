import { MCPServer } from 'model-context-protocol';
import axios from 'axios';

export async function startServer(port = 3017, toolName: string, toolUrl: string) {
    const server = new MCPServer({ port });
    server.registerTool('checkTool', {
        title: 'Agent Tool Health Check',
        parameters: { type: 'object', properties: {} },
        returns: { type: 'object', properties: { available: { type: 'boolean' }, latencyMs: { type: 'number' } } }
    }, async () => {
        const start = Date.now();
        try {
            await axios.post(toolUrl, { jsonrpc: '2.0', method: 'listTools', params: [], id: 1 });
            const latency = Date.now() - start;
            return { available: true, latencyMs: latency };
        } catch {
            return { available: false, latencyMs: -1 };
        }
    });
    await server.listen();
}