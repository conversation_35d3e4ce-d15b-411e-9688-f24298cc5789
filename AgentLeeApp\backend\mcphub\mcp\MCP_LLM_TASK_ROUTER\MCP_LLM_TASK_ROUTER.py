class LLMTaskRouterMCP:
    def __init__(self):
        self.routes = {
            "reasoning": "AZR",
            "planning": "PHI-3",
            "summarization": "GEMINI",
            "validation": "QWEN",
            "memory": "ECHO",
            "longform": "LLAMA"
        }

    def route_task(self, task_type: str):
        agent = self.routes.get(task_type.lower())
        if not agent:
            return {"error": "Unknown task type", "available": list(self.routes.keys())}
        return {"task_type": task_type, "assigned_agent": agent}

    def list_routes(self):
        return self.routes

