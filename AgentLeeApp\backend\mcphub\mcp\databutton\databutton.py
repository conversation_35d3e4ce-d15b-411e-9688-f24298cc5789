import requests

class DataButtonMCP:
    def __init__(self, base_url="https://api.databutton.com"):
        self.base_url = base_url

    def list_apps(self):
        url = f"{self.base_url}/v1/apps"
        response = requests.get(url)
        response.raise_for_status()
        return response.json()

    def run_app(self, app_id: str, payload: dict):
        url = f"{self.base_url}/v1/apps/{app_id}/run"
        response = requests.post(url, json=payload)
        response.raise_for_status()
        return response.json()

    def get_status(self, run_id: str):
        url = f"{self.base_url}/v1/runs/{run_id}/status"
        response = requests.get(url)
        response.raise_for_status()
        return response.json()
