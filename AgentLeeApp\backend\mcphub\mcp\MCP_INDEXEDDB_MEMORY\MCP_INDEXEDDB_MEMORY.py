import json
import os

class IndexedDBMemoryMCP:
    def __init__(self, storage_dir="memory"):
        os.makedirs(storage_dir, exist_ok=True)
        self.storage_dir = storage_dir

    def _get_path(self, db_name: str):
        return os.path.join(self.storage_dir, f"{db_name}.json")

    def save(self, db_name: str, key: str, value):
        path = self._get_path(db_name)
        data = {}
        if os.path.exists(path):
            with open(path, "r") as f:
                data = json.load(f)
        data[key] = value
        with open(path, "w") as f:
            json.dump(data, f, indent=2)
        return {"status": "saved", "db": db_name, "key": key}

    def retrieve(self, db_name: str, key: str):
        path = self._get_path(db_name)
        if os.path.exists(path):
            with open(path, "r") as f:
                data = json.load(f)
            return data.get(key, None)
        return {"error": "Database not found"}

    def list_keys(self, db_name: str):
        path = self._get_path(db_name)
        if os.path.exists(path):
            with open(path, "r") as f:
                data = json.load(f)
            return list(data.keys())
        return {"error": "Database not found"}
