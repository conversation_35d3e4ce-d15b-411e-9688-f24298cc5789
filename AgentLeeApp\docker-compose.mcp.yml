version: '3.8'

services:
  # MCP Gateway - Central routing and load balancing
  mcp-gateway:
    build:
      context: ./backend/mcphub
      dockerfile: Dockerfile.gateway
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HEALTH_CHECK_INTERVAL=30000
      - REQUEST_TIMEOUT=30000
      - ENABLE_CORS=true
      - ALLOWED_ORIGINS=http://localhost:3000,https://agentlee.fly.dev
    volumes:
      - ./backend/mcphub/mcp_settings.json:/app/mcp_settings.json:ro
    networks:
      - mcp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MCP Hub - Main MCP services
  mcp-hub:
    build:
      context: ./backend/mcphub
      dockerfile: Dockerfile
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=production
      - PORT=3002
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - NOTION_TOKEN=${NOTION_TOKEN}
    volumes:
      - mcp-screenshots:/app/screenshots
      - mcp-logs:/app/logs
    networks:
      - mcp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    depends_on:
      - redis
      - postgres

  # Health Monitor
  mcp-health-monitor:
    build:
      context: ./backend/mcphub
      dockerfile: Dockerfile.monitor
    environment:
      - NODE_ENV=production
      - CHECK_INTERVAL=30000
      - RETENTION_PERIOD=24
      - ENABLE_PERSISTENCE=true
      - PERSISTENCE_PATH=/data/health
    volumes:
      - mcp-health-data:/data/health
      - ./backend/mcphub/mcp_settings.json:/app/mcp_settings.json:ro
    networks:
      - mcp-network
    restart: unless-stopped
    depends_on:
      - mcp-gateway
      - mcp-hub

  # Webhook System
  mcp-webhooks:
    build:
      context: ./backend/mcphub
      dockerfile: Dockerfile.webhooks
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - ENABLE_SECURITY=true
      - ENABLE_LOGGING=true
      - GITHUB_WEBHOOK_SECRET=${GITHUB_WEBHOOK_SECRET}
      - DISCORD_WEBHOOK_SECRET=${DISCORD_WEBHOOK_SECRET}
      - TELEGRAM_WEBHOOK_SECRET=${TELEGRAM_WEBHOOK_SECRET}
      - MCP_GATEWAY_URL=http://mcp-gateway:3000
    networks:
      - mcp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    depends_on:
      - mcp-gateway

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - mcp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru

  # PostgreSQL for persistent data
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=agentlee_mcp
      - POSTGRES_USER=agentlee
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-agentlee123}
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./backend/mcphub/sql/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - mcp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U agentlee -d agentlee_mcp"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx reverse proxy and load balancer
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx-logs:/var/log/nginx
    networks:
      - mcp-network
    restart: unless-stopped
    depends_on:
      - mcp-gateway
      - mcp-webhooks
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    networks:
      - mcp-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Grafana for monitoring dashboards
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3003:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - mcp-network
    restart: unless-stopped
    depends_on:
      - prometheus

  # Jaeger for distributed tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - mcp-network
    restart: unless-stopped

  # File storage service
  minio:
    image: minio/minio:latest
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=${MINIO_ROOT_USER:-agentlee}
      - MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD:-agentlee123}
    volumes:
      - minio-data:/data
    networks:
      - mcp-network
    restart: unless-stopped
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  mcp-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  redis-data:
    driver: local
  postgres-data:
    driver: local
  mcp-screenshots:
    driver: local
  mcp-logs:
    driver: local
  mcp-health-data:
    driver: local
  nginx-logs:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  minio-data:
    driver: local

# Development override
# Use: docker-compose -f docker-compose.mcp.yml -f docker-compose.dev.yml up
---
version: '3.8'

# docker-compose.dev.yml
services:
  mcp-gateway:
    environment:
      - NODE_ENV=development
      - DEBUG=mcp:*
    volumes:
      - ./backend/mcphub:/app:ro
      - /app/node_modules
    command: npm run dev

  mcp-hub:
    environment:
      - NODE_ENV=development
      - DEBUG=mcp:*
    volumes:
      - ./backend/mcphub:/app:ro
      - /app/node_modules
    command: npm run dev

  mcp-health-monitor:
    environment:
      - NODE_ENV=development
      - DEBUG=mcp:*
    volumes:
      - ./backend/mcphub:/app:ro
      - /app/node_modules
    command: npm run dev:monitor

  mcp-webhooks:
    environment:
      - NODE_ENV=development
      - DEBUG=mcp:*
    volumes:
      - ./backend/mcphub:/app:ro
      - /app/node_modules
    command: npm run dev:webhooks
