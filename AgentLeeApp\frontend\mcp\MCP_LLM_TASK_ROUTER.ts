import { MCPServer } from 'model-context-protocol';

export async function startServer(port = 3034) {
    const server = new MCPServer({ port });
    server.registerTool('routeTask', {
        title: 'LLM Task Router',
        parameters: {
            type: 'object',
            properties: { taskType: { type: 'string' }, payload: { type: 'object' } },
            required: ['taskType', 'payload']
        },
        returns: { type: 'object', properties: { forwardedTo: { type: 'string' }, result: { type: 'any' } } }
    }, async ({ taskType, payload }) => {
        // e.g. if taskType matches built-in tools like 'tts', 'summarize', etc.
        // stub implementation routes by taskType name
        return { forwardedTo: taskType, result: payload };
    });
    await server.listen();
}