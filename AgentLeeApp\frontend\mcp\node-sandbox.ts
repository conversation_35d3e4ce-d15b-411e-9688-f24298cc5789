/**
 * Node Sandbox MCP Server
 * Provides secure JavaScript execution environment
 */

import { MCPServer, MCPTool, createToolSchema, ToolPropertyTypes } from '../mcp-base';

// Simple sandbox implementation without vm2 dependency
class SimpleSandbox {
  private timeout: number;

  constructor(timeout: number = 5000) {
    this.timeout = timeout;
  }

  async run(code: string, context: Record<string, any> = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error('Code execution timeout'));
      }, this.timeout);

      try {
        // Create a simple sandbox using Function constructor
        // Note: This is a basic implementation. For production, consider using a proper sandbox library
        const func = new Function('context', `
          const { ${Object.keys(context).join(', ')} } = context;
          return (async () => {
            ${code}
          })();
        `);

        const result = func(context);

        if (result instanceof Promise) {
          result
            .then((res) => {
              clearTimeout(timer);
              resolve(res);
            })
            .catch((err) => {
              clearTimeout(timer);
              reject(err);
            });
        } else {
          clearTimeout(timer);
          resolve(result);
        }
      } catch (error) {
        clearTimeout(timer);
        reject(error);
      }
    });
  }
}

class NodeSandboxMCPServer extends MCPServer {
  private sandbox: SimpleSandbox;

  constructor() {
    super({
      name: 'Node Sandbox MCP Server',
      version: '1.0.0',
      port: 3008
    });

    this.sandbox = new SimpleSandbox(5000);
    this.setupTools();
  }

  private setupTools(): void {
    // Run Code Snippet Tool
    const runSnippetTool: MCPTool = {
      name: 'runSnippet',
      description: 'Execute JavaScript code in a secure sandbox environment',
      inputSchema: createToolSchema({
        code: ToolPropertyTypes.string('JavaScript code to execute'),
        timeout: ToolPropertyTypes.number('Execution timeout in milliseconds', 1000, 10000),
        context: ToolPropertyTypes.object('Variables to make available in the sandbox', {})
      }, ['code']),
      handler: async ({ code, timeout = 5000, context = {} }: {
        code: string;
        timeout?: number;
        context?: Record<string, any>
      }) => {
        try {
          // Create sandbox with specified timeout
          const sandbox = new SimpleSandbox(timeout);

          // Add safe built-in functions to context
          const safeContext = {
            ...context,
            console: {
              log: (...args: any[]) => console.log('[SANDBOX]', ...args),
              error: (...args: any[]) => console.error('[SANDBOX]', ...args),
              warn: (...args: any[]) => console.warn('[SANDBOX]', ...args)
            },
            Math,
            Date,
            JSON,
            setTimeout: (fn: () => void, delay: number) => {
              if (delay > 1000) delay = 1000; // Limit delay
              return setTimeout(fn, delay);
            }
          };

          const result = await sandbox.run(code, safeContext);

          return {
            success: true,
            result: result,
            output: String(result),
            executionTime: Date.now()
          };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';

          return {
            success: false,
            error: errorMessage,
            code: code.substring(0, 100) + (code.length > 100 ? '...' : '')
          };
        }
      }
    };

    // Evaluate Expression Tool
    const evaluateExpressionTool: MCPTool = {
      name: 'evaluateExpression',
      description: 'Evaluate a simple JavaScript expression',
      inputSchema: createToolSchema({
        expression: ToolPropertyTypes.string('JavaScript expression to evaluate'),
        variables: ToolPropertyTypes.object('Variables to use in the expression', {})
      }, ['expression']),
      handler: async ({ expression, variables = {} }: {
        expression: string;
        variables?: Record<string, any>
      }) => {
        try {
          // Simple expression evaluation
          const func = new Function(...Object.keys(variables), `return ${expression}`);
          const result = func(...Object.values(variables));

          return {
            success: true,
            expression,
            result,
            type: typeof result
          };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';

          return {
            success: false,
            error: errorMessage,
            expression
          };
        }
      }
    };

    // Validate Syntax Tool
    const validateSyntaxTool: MCPTool = {
      name: 'validateSyntax',
      description: 'Check if JavaScript code has valid syntax',
      inputSchema: createToolSchema({
        code: ToolPropertyTypes.string('JavaScript code to validate')
      }, ['code']),
      handler: async ({ code }: { code: string }) => {
        try {
          new Function(code);

          return {
            valid: true,
            code: code.substring(0, 100) + (code.length > 100 ? '...' : '')
          };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';

          return {
            valid: false,
            error: errorMessage,
            code: code.substring(0, 100) + (code.length > 100 ? '...' : '')
          };
        }
      }
    };

    // Add all tools
    this.addTool(runSnippetTool);
    this.addTool(evaluateExpressionTool);
    this.addTool(validateSyntaxTool);
  }

  protected override getMetrics(): any {
    const baseMetrics = super.getMetrics();
    return {
      ...baseMetrics,
      sandboxTimeout: 5000,
      securityLevel: 'basic'
    };
  }
}

// Export for use in other modules
export async function startServer(port = 3008): Promise<NodeSandboxMCPServer> {
  const server = new NodeSandboxMCPServer();
  await server.start(port);
  return server;
}

// Start server if this file is run directly
if (require.main === module) {
  startServer().catch(console.error);
}
