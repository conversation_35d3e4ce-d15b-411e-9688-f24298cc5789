import sounddevice as sd
import queue
import numpy as np

class VoiceLoopMCP:
    def __init__(self, samplerate=16000, channels=1, block_duration=100):
        self.q = queue.Queue()
        self.samplerate = samplerate
        self.channels = channels
        self.block_duration = block_duration  # ms
        self.stream = None

    def _callback(self, indata, frames, time, status):
        if status:
            print(status)
        self.q.put(indata.copy())

    def start_loopback(self):
        try:
            self.stream = sd.Stream(
                samplerate=self.samplerate,
                channels=self.channels,
                dtype='float32',
                callback=self._callback
            )
            self.stream.start()
            return {"status": "voice loop started"}
        except Exception as e:
            return {"error": str(e)}

    def read_chunk(self):
        try:
            data = self.q.get_nowait()
            return {"samples": data.tolist()}
        except queue.Empty:
            return {"error": "No data yet."}

    def stop_loopback(self):
        if self.stream:
            self.stream.stop()
            self.stream.close()
        return {"status": "voice loop stopped"}
