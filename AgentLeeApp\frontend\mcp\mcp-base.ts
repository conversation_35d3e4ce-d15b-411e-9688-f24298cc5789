/**
 * MCP Base Classes and Types
 * Provides base functionality for all MCP servers
 */

import { EventEmitter } from 'events';
import express, { Request, Response } from 'express';

// MCP Tool Interface
export interface MCPTool {
  name: string;
  description: string;
  inputSchema: {
    type: 'object';
    properties: Record<string, any>;
    required?: string[];
  };
  handler: (params: any) => Promise<any>;
}

// MCP Resource Interface
export interface MCPResource {
  uri: string;
  name: string;
  description?: string;
  mimeType?: string;
}

// MCP Server Configuration
export interface MCPServerConfig {
  name: string;
  version: string;
  port?: number;
  enableHealthCheck?: boolean;
  enableMetrics?: boolean;
  timeout?: number;
}

// MCP Server Response
export interface MCPResponse {
  success: boolean;
  data?: any;
  error?: string;
  metadata?: {
    timestamp: Date;
    executionTime: number;
    version: string;
  };
}

// Base MCP Server Class
export class MCPServer extends EventEmitter {
  protected config: MCPServerConfig;
  protected tools: Map<string, MCPTool> = new Map();
  protected resources: Map<string, MCPResource> = new Map();
  protected app: express.Application;
  protected server?: any;
  protected isRunning: boolean = false;
  protected startTime: Date = new Date();

  constructor(config: MCPServerConfig) {
    super();
    this.config = {
      enableHealthCheck: true,
      enableMetrics: true,
      timeout: 30000,
      ...config
    };
    
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
  }

  private setupMiddleware(): void {
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));
    
    // CORS
    this.app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
      if (req.method === 'OPTIONS') {
        res.sendStatus(200);
      } else {
        next();
      }
    });

    // Request timeout
    this.app.use((req, res, next) => {
      req.setTimeout(this.config.timeout || 30000);
      next();
    });
  }

  private setupRoutes(): void {
    // Health check endpoint
    if (this.config.enableHealthCheck) {
      this.app.get('/health', (req: Request, res: Response) => {
        res.json({
          status: 'healthy',
          name: this.config.name,
          version: this.config.version,
          uptime: Date.now() - this.startTime.getTime(),
          timestamp: new Date().toISOString(),
          tools: Array.from(this.tools.keys()),
          resources: Array.from(this.resources.keys())
        });
      });
    }

    // Tools endpoint
    this.app.get('/tools', (req: Request, res: Response) => {
      const toolList = Array.from(this.tools.values()).map(tool => ({
        name: tool.name,
        description: tool.description,
        inputSchema: tool.inputSchema
      }));
      
      res.json({
        success: true,
        data: toolList,
        metadata: this.getResponseMetadata()
      });
    });

    // Execute tool endpoint
    this.app.post('/tools/:toolName', async (req: Request, res: Response) => {
      const startTime = Date.now();
      const toolName = req.params.toolName;
      
      try {
        const tool = this.tools.get(toolName);
        if (!tool) {
          return res.status(404).json({
            success: false,
            error: `Tool '${toolName}' not found`,
            metadata: this.getResponseMetadata(startTime)
          });
        }

        const result = await tool.handler(req.body);
        
        res.json({
          success: true,
          data: result,
          metadata: this.getResponseMetadata(startTime)
        });

        this.emit('toolExecuted', { tool: toolName, params: req.body, result });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';

        res.status(500).json({
          success: false,
          error: errorMessage,
          metadata: this.getResponseMetadata(startTime)
        });

        this.emit('toolError', { tool: toolName, params: req.body, error: errorMessage });
      }
    });

    // Resources endpoint
    this.app.get('/resources', (req: Request, res: Response) => {
      const resourceList = Array.from(this.resources.values());
      
      res.json({
        success: true,
        data: resourceList,
        metadata: this.getResponseMetadata()
      });
    });

    // Get specific resource
    this.app.get('/resources/:resourceUri', async (req: Request, res: Response) => {
      const startTime = Date.now();
      const resourceUri = decodeURIComponent(req.params.resourceUri);
      
      try {
        const resource = this.resources.get(resourceUri);
        if (!resource) {
          return res.status(404).json({
            success: false,
            error: `Resource '${resourceUri}' not found`,
            metadata: this.getResponseMetadata(startTime)
          });
        }

        // Override this method in subclasses to provide actual resource content
        const content = await this.getResourceContent(resource);
        
        res.json({
          success: true,
          data: { resource, content },
          metadata: this.getResponseMetadata(startTime)
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        
        res.status(500).json({
          success: false,
          error: errorMessage,
          metadata: this.getResponseMetadata(startTime)
        });
      }
    });

    // Metrics endpoint
    if (this.config.enableMetrics) {
      this.app.get('/metrics', (req: Request, res: Response) => {
        res.json({
          success: true,
          data: this.getMetrics(),
          metadata: this.getResponseMetadata()
        });
      });
    }
  }

  protected getResponseMetadata(startTime?: number): any {
    return {
      timestamp: new Date(),
      executionTime: startTime ? Date.now() - startTime : 0,
      version: this.config.version,
      server: this.config.name
    };
  }

  protected async getResourceContent(resource: MCPResource): Promise<any> {
    // Override in subclasses
    return { message: 'Resource content not implemented' };
  }

  protected getMetrics(): any {
    return {
      uptime: Date.now() - this.startTime.getTime(),
      toolCount: this.tools.size,
      resourceCount: this.resources.size,
      memoryUsage: process.memoryUsage(),
      isRunning: this.isRunning
    };
  }

  // Tool management
  addTool(tool: MCPTool): void {
    this.tools.set(tool.name, tool);
    this.emit('toolAdded', tool);
  }

  removeTool(name: string): boolean {
    const removed = this.tools.delete(name);
    if (removed) {
      this.emit('toolRemoved', name);
    }
    return removed;
  }

  getTool(name: string): MCPTool | undefined {
    return this.tools.get(name);
  }

  getTools(): MCPTool[] {
    return Array.from(this.tools.values());
  }

  // Resource management
  addResource(resource: MCPResource): void {
    this.resources.set(resource.uri, resource);
    this.emit('resourceAdded', resource);
  }

  removeResource(uri: string): boolean {
    const removed = this.resources.delete(uri);
    if (removed) {
      this.emit('resourceRemoved', uri);
    }
    return removed;
  }

  getResource(uri: string): MCPResource | undefined {
    return this.resources.get(uri);
  }

  getResources(): MCPResource[] {
    return Array.from(this.resources.values());
  }

  // Server lifecycle
  async start(port?: number): Promise<void> {
    const serverPort = port || this.config.port || 3000;
    
    return new Promise((resolve, reject) => {
      this.server = this.app.listen(serverPort, (err?: Error) => {
        if (err) {
          reject(err);
          return;
        }

        this.isRunning = true;
        this.startTime = new Date();
        
        console.log(`🚀 MCP Server '${this.config.name}' started on port ${serverPort}`);
        this.emit('started', { port: serverPort });
        resolve();
      });
    });
  }

  async stop(): Promise<void> {
    if (!this.server) {
      return;
    }

    return new Promise((resolve) => {
      this.server.close(() => {
        this.isRunning = false;
        console.log(`🛑 MCP Server '${this.config.name}' stopped`);
        this.emit('stopped');
        resolve();
      });
    });
  }

  // Utility methods
  isHealthy(): boolean {
    return this.isRunning;
  }

  getInfo(): any {
    return {
      name: this.config.name,
      version: this.config.version,
      uptime: Date.now() - this.startTime.getTime(),
      isRunning: this.isRunning,
      toolCount: this.tools.size,
      resourceCount: this.resources.size
    };
  }
}

// Utility function to create tool schema
export function createToolSchema(properties: Record<string, any>, required: string[] = []): any {
  return {
    type: 'object',
    properties,
    required
  };
}

// Common property types for tools
export const ToolPropertyTypes = {
  string: (description: string, defaultValue?: string) => ({
    type: 'string',
    description,
    ...(defaultValue && { default: defaultValue })
  }),
  
  number: (description: string, min?: number, max?: number) => ({
    type: 'number',
    description,
    ...(min !== undefined && { minimum: min }),
    ...(max !== undefined && { maximum: max })
  }),
  
  boolean: (description: string, defaultValue?: boolean) => ({
    type: 'boolean',
    description,
    ...(defaultValue !== undefined && { default: defaultValue })
  }),
  
  array: (description: string, itemType: any) => ({
    type: 'array',
    description,
    items: itemType
  }),
  
  object: (description: string, properties: Record<string, any>) => ({
    type: 'object',
    description,
    properties
  })
};

export default MCPServer;
