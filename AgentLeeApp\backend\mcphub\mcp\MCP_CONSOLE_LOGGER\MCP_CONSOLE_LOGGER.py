import logging
import os
from datetime import datetime

class ConsoleLoggerMCP:
    def __init__(self, log_file="logs/console.log"):
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        logging.basicConfig(
            filename=log_file,
            level=logging.INFO,
            format="%(asctime)s [%(levelname)s] %(message)s"
        )
        self.logger = logging.getLogger("ConsoleLogger")

    def log_message(self, message: str, level: str = "info"):
        level = level.lower()
        if level == "debug":
            self.logger.debug(message)
        elif level == "warning":
            self.logger.warning(message)
        elif level == "error":
            self.logger.error(message)
        else:
            self.logger.info(message)
        return {"status": "logged", "level": level, "message": message}

    def get_latest_logs(self, lines=10):
        try:
            with open("logs/console.log", "r") as f:
                content = f.readlines()
            return {"logs": content[-lines:]}
        except Exception as e:
            return {"error": str(e)}
