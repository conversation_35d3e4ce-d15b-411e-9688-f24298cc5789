import { MCPServer } from 'model-context-protocol';
import fs from 'fs/promises';

export async function startServer(port = 3027) {
    const server = new MCPServer({ port });
    server.registerTool('readFile', {
        title: 'Read File',
        parameters: { type: 'object', properties: { path: { type: 'string' } }, required: ['path'] },
        returns: { type: 'object', properties: { content: { type: 'string' } } }
    }, async ({ path }) => ({ content: await fs.readFile(path, 'utf-8') }));
    server.registerTool('writeFile', {
        title: 'Write File',
        parameters: { type: 'object', properties: { path: { type: 'string' }, content: { type: 'string' } }, required: ['path', 'content'] },
        returns: { type: 'object', properties: { success: { type: 'boolean' } } }
    }, async ({ path, content }) => {
        await fs.writeFile(path, content, 'utf-8');
        return { success: true };
    });
    await server.listen();
}