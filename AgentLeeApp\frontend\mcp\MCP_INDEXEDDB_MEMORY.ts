import { MCPServer } from 'model-context-protocol';
import { openDB } from 'idb';

export async function startServer(port = 3032, dbName: string) {
    const db = await openDB(dbName, 1, {
        upgrade(db) {
            db.createObjectStore('keyval');
        }
    });
    const server = new MCPServer({ port });
    server.registerTool('indexedGet', {
        title: 'IndexedDB Get',
        parameters: { type: 'object', properties: { key: { type: 'string' } }, required: ['key'] },
        returns: { type: 'object', properties: { value: { type: 'any' } } }
    }, async ({ key }) => ({ value: await db.get('keyval', key) }));
    server.registerTool('indexedSet', {
        title: 'IndexedDB Set',
        parameters: {
            type: 'object',
            properties: { key: { type: 'string' }, value: { type: 'any' } },
            required: ['key', 'value']
        },
        returns: { type: 'object', properties: { success: { type: 'boolean' } } }
    }, async ({ key, value }) => {
        await db.put('keyval', value, key);
        return { success: true };
    });
    await server.listen();
}