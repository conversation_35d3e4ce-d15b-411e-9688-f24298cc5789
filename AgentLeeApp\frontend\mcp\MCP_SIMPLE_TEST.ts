
import { MCPServer, MCPTool, createToolSchema, ToolPropertyTypes } from '../mcp-base';

class SimpleTestMCPServer extends MCPServer {
  constructor() {
    super({
      name: 'Simple Test MCP Server',
      version: '1.0.0',
      port: 3010
    });

    this.setupTools();
  }

  private setupTools(): void {
    const simpleTestTool: MCPTool = {
      name: 'simpleTest',
      description: 'A simple test MCP that returns a string',
      inputSchema: createToolSchema({
        message: ToolPropertyTypes.string('Test message', 'Hello World!')
      }),
      handler: async ({ message = 'Hello World!' }: { message?: string }) => {
        return {
          success: true,
          result: `Simple test response: ${message}`,
          timestamp: new Date().toISOString()
        };
      }
    };

    this.addTool(simpleTestTool);
  }
}

export async function startServer(port = 3010): Promise<SimpleTestMCPServer> {
  const server = new SimpleTestMCPServer();
  await server.start(port);
  return server;
}

// Start server if this file is run directly
if (require.main === module) {
  startServer().catch(console.error);
}
