from fastapi import FastAPI, HTTPException
from MCP_TELEGRAM_BOT import TelegramBotMCP

# Replace with your bot token
mcp = TelegramBotMCP(token="YOUR_TELEGRAM_BOT_TOKEN")

app = FastAPI()

@app.get("/telegram/start")
def start_bot():
    try:
        return mcp.run_bot()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/telegram/stop")
def stop_bot():
    try:
        return mcp.stop_bot()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
