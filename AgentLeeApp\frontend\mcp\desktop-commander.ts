import { MCPServer } from 'model-context-protocol';
import { exec } from 'child_process';

export async function startServer(port = 3011) {
    const server = new MCPServer({ port });
    server.registerTool('runCommand', {
        title: 'Run Short Command',
        description: 'Run a safe shell command on host',
        parameters: { type: 'object', properties: { command: { type: 'string' } }, required: ['command'] },
        returns: { type: 'object', properties: { stdout: { type: 'string' }, stderr: { type: 'string' } } }
    }, async ({ command }) => {
        return new Promise((resolve) => {
            exec(command, { timeout: 5000, maxBuffer: 1024 * 1024 }, (err, stdout, stderr) => {
                resolve({ stdout: stdout.trim(), stderr: stderr.trim() });
            });
        });
    });
    await server.listen();
}