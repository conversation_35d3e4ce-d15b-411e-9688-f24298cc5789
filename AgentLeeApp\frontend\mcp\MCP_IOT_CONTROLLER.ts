import { MCPServer } from '../mcp-base';
import { MCPTool, MCPResource, ToolPropertyTypes, createToolSchema } from '../mcp-base';
import { Request, Response } from 'express';
import * as mqtt from 'mqtt';
import axios from 'axios';

interface IoTDevice {
  id: string;
  name: string;
  type: 'light' | 'camera' | 'sensor' | 'lock' | 'speaker' | 'thermostat' | 'switch' | 'outlet';
  protocol: 'wifi' | 'bluetooth' | 'zigbee' | 'zwave' | 'matter';
  status: 'online' | 'offline' | 'unknown';
  capabilities: string[];
  location: string;
  manufacturer?: string;
  model?: string;
  ip_address?: string;
  mac_address?: string;
  last_seen?: Date;
  attributes: Record<string, any>;
}

interface HomeAssistantConfig {
  url: string;
  token: string;
  enabled: boolean;
}

interface MQTTConfig {
  broker: string;
  port: number;
  username?: string;
  password?: string;
  enabled: boolean;
}

interface BluetoothConfig {
  enabled: boolean;
  scan_interval: number;
  auto_pair: boolean;
}

class IoTControllerMCPServer extends MCPServer {
  private devices: Map<string, IoTDevice> = new Map();
  private mqttClient?: mqtt.MqttClient;
  private homeAssistantConfig: HomeAssistantConfig;
  private mqttConfig: MQTTConfig;
  private bluetoothConfig: BluetoothConfig;
  private deviceDiscoveryInterval?: NodeJS.Timeout;

  constructor(port: number = 3010) {
    super({
      name: 'IoT Controller MCP Server',
      version: '1.0.0',
      port,
      enableMetrics: true,
      enableHealthCheck: true
    });

    // Load configuration from environment or defaults
    this.homeAssistantConfig = {
      url: process.env.HOME_ASSISTANT_URL || 'http://homeassistant.local:8123',
      token: process.env.HOME_ASSISTANT_TOKEN || '',
      enabled: process.env.HOME_ASSISTANT_ENABLED === 'true'
    };

    this.mqttConfig = {
      broker: process.env.MQTT_BROKER || 'mqtt://localhost',
      port: parseInt(process.env.MQTT_PORT || '1883'),
      username: process.env.MQTT_USERNAME,
      password: process.env.MQTT_PASSWORD,
      enabled: process.env.MQTT_ENABLED === 'true'
    };

    this.bluetoothConfig = {
      enabled: process.env.BLUETOOTH_ENABLED === 'true',
      scan_interval: parseInt(process.env.BLUETOOTH_SCAN_INTERVAL || '30000'),
      auto_pair: process.env.BLUETOOTH_AUTO_PAIR === 'true'
    };

    this.initializeTools();
    this.initializeResources();
    this.initializeConnections();
  }

  private initializeTools() {
    // Device Discovery Tool
    const discoverDevicesTool: MCPTool = {
      name: 'discoverDevices',
      description: 'Discover IoT devices on the network via Wi-Fi, Bluetooth, and other protocols',
      inputSchema: createToolSchema({
        protocol: ToolPropertyTypes.string('Protocol to scan: wifi, bluetooth, all', false),
        timeout: ToolPropertyTypes.number('Scan timeout in seconds', false)
      }),
      handler: async (params: any) => {
        return await this.discoverDevices(params.protocol || 'all', params.timeout || 30);
      }
    };

    // Control Device Tool
    const controlDeviceTool: MCPTool = {
      name: 'controlDevice',
      description: 'Control an IoT device (turn on/off, adjust settings, etc.)',
      inputSchema: createToolSchema({
        device_id: ToolPropertyTypes.string('Device ID or name'),
        action: ToolPropertyTypes.string('Action to perform (on, off, toggle, set_brightness, etc.)'),
        value: ToolPropertyTypes.string('Value for the action (brightness level, temperature, etc.)', false)
      }),
      handler: async (params: any) => {
        return await this.controlDevice(params.device_id, params.action, params.value);
      }
    };

    // Get Device Status Tool
    const getDeviceStatusTool: MCPTool = {
      name: 'getDeviceStatus',
      description: 'Get current status and attributes of an IoT device',
      inputSchema: createToolSchema({
        device_id: ToolPropertyTypes.string('Device ID or name')
      }),
      handler: async (params: any) => {
        return await this.getDeviceStatus(params.device_id);
      }
    };

    // List Devices Tool
    const listDevicesTool: MCPTool = {
      name: 'listDevices',
      description: 'List all discovered IoT devices with their current status',
      inputSchema: createToolSchema({
        type: ToolPropertyTypes.string('Filter by device type', false),
        location: ToolPropertyTypes.string('Filter by location', false),
        status: ToolPropertyTypes.string('Filter by status (online, offline)', false)
      }),
      handler: async (params: any) => {
        return await this.listDevices(params.type, params.location, params.status);
      }
    };

    // Scene Control Tool
    const controlSceneTool: MCPTool = {
      name: 'controlScene',
      description: 'Activate a predefined scene or automation (e.g., "movie night", "bedtime")',
      inputSchema: createToolSchema({
        scene_name: ToolPropertyTypes.string('Name of the scene to activate'),
        parameters: ToolPropertyTypes.object('Optional parameters for the scene', {})
      }),
      handler: async (params: any) => {
        return await this.controlScene(params.scene_name, params.parameters);
      }
    };

    // Voice Command Tool
    const processVoiceCommandTool: MCPTool = {
      name: 'processVoiceCommand',
      description: 'Process natural language voice commands for IoT device control',
      inputSchema: createToolSchema({
        command: ToolPropertyTypes.string('Natural language command (e.g., "turn on living room lights")'),
        speaker: ToolPropertyTypes.string('Speaker/user identifier')
      }, ['command']),
      handler: async (params: any) => {
        return await this.processVoiceCommand(params.command, params.speaker);
      }
    };

    // Register all tools
    this.addTool(discoverDevicesTool);
    this.addTool(controlDeviceTool);
    this.addTool(getDeviceStatusTool);
    this.addTool(listDevicesTool);
    this.addTool(controlSceneTool);
    this.addTool(processVoiceCommandTool);
  }

  private initializeResources() {
    // Devices Resource
    const devicesResource: MCPResource = {
      uri: 'iot://devices',
      name: 'IoT Devices',
      description: 'List of all discovered IoT devices',
      mimeType: 'application/json'
    };

    // Device Types Resource
    const deviceTypesResource: MCPResource = {
      uri: 'iot://device-types',
      name: 'Device Types',
      description: 'Supported IoT device types and capabilities',
      mimeType: 'application/json'
    };

    // Network Status Resource
    const networkStatusResource: MCPResource = {
      uri: 'iot://network-status',
      name: 'Network Status',
      description: 'Current network connectivity and protocol status',
      mimeType: 'application/json'
    };

    this.addResource(devicesResource);
    this.addResource(deviceTypesResource);
    this.addResource(networkStatusResource);
  }

  private async initializeConnections() {
    try {
      // Initialize MQTT connection
      if (this.mqttConfig.enabled) {
        await this.initializeMQTT();
      }

      // Initialize Home Assistant connection
      if (this.homeAssistantConfig.enabled) {
        await this.initializeHomeAssistant();
      }

      // Initialize Bluetooth scanning
      if (this.bluetoothConfig.enabled) {
        await this.initializeBluetooth();
      }

      // Start device discovery
      this.startDeviceDiscovery();

      console.log('🏠 IoT Controller initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize IoT Controller:', error);
    }
  }

  private async initializeMQTT() {
    try {
      const options: mqtt.IClientOptions = {
        port: this.mqttConfig.port,
        username: this.mqttConfig.username,
        password: this.mqttConfig.password,
        reconnectPeriod: 5000,
        connectTimeout: 30000
      };

      this.mqttClient = mqtt.connect(this.mqttConfig.broker, options);

      this.mqttClient.on('connect', () => {
        console.log('📡 Connected to MQTT broker');
        // Subscribe to device topics
        this.mqttClient?.subscribe('homeassistant/+/+/state');
        this.mqttClient?.subscribe('zigbee2mqtt/+');
        this.mqttClient?.subscribe('tasmota/+/SENSOR');
      });

      this.mqttClient.on('message', (topic, message) => {
        this.handleMQTTMessage(topic, message.toString());
      });

      this.mqttClient.on('error', (error) => {
        console.error('❌ MQTT connection error:', error);
      });

    } catch (error) {
      console.error('❌ Failed to initialize MQTT:', error);
    }
  }

  private async initializeHomeAssistant() {
    try {
      if (!this.homeAssistantConfig.token) {
        console.warn('⚠️ Home Assistant token not provided');
        return;
      }

      // Test connection
      const response = await axios.get(`${this.homeAssistantConfig.url}/api/`, {
        headers: {
          'Authorization': `Bearer ${this.homeAssistantConfig.token}`,
          'Content-Type': 'application/json'
        },
        timeout: 5000
      });

      if (response.status === 200) {
        console.log('🏠 Connected to Home Assistant');
        await this.loadHomeAssistantDevices();
      }
    } catch (error) {
      console.error('❌ Failed to connect to Home Assistant:', error);
    }
  }

  private async initializeBluetooth() {
    try {
      // Note: In a real implementation, you would use a Bluetooth library like 'noble' or 'bluetooth-hci-socket'
      // For now, we'll simulate Bluetooth functionality
      console.log('📶 Bluetooth scanning initialized (simulated)');
      
      // Start periodic Bluetooth scanning
      setInterval(() => {
        this.scanBluetoothDevices();
      }, this.bluetoothConfig.scan_interval);

    } catch (error) {
      console.error('❌ Failed to initialize Bluetooth:', error);
    }
  }

  private startDeviceDiscovery() {
    // Periodic device discovery and status updates
    this.deviceDiscoveryInterval = setInterval(async () => {
      await this.discoverDevices('all', 10);
    }, 60000); // Every minute
  }

  private async discoverDevices(protocol: string = 'all', timeout: number = 30): Promise<any> {
    const discoveredDevices: IoTDevice[] = [];

    try {
      if (protocol === 'all' || protocol === 'wifi') {
        const wifiDevices = await this.discoverWiFiDevices(timeout);
        discoveredDevices.push(...wifiDevices);
      }

      if (protocol === 'all' || protocol === 'bluetooth') {
        const bluetoothDevices = await this.discoverBluetoothDevices(timeout);
        discoveredDevices.push(...bluetoothDevices);
      }

      if (protocol === 'all' || protocol === 'homeassistant') {
        const haDevices = await this.discoverHomeAssistantDevices();
        discoveredDevices.push(...haDevices);
      }

      // Update device registry
      discoveredDevices.forEach(device => {
        this.devices.set(device.id, device);
      });

      return {
        success: true,
        discovered_count: discoveredDevices.length,
        devices: discoveredDevices,
        total_devices: this.devices.size
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Discovery failed'
      };
    }
  }

  private async discoverWiFiDevices(timeout: number): Promise<IoTDevice[]> {
    const devices: IoTDevice[] = [];

    try {
      // Simulate Wi-Fi device discovery using network scanning
      // In a real implementation, you would use libraries like 'node-nmap' or 'ping'
      const commonDeviceIPs = [
        '*************', '*************', '*************',
        '*************', '*************', '*************'
      ];

      for (const ip of commonDeviceIPs) {
        try {
          // Simulate device detection
          const device: IoTDevice = {
            id: `wifi_${ip.replace(/\./g, '_')}`,
            name: `Smart Device ${ip}`,
            type: 'light',
            protocol: 'wifi',
            status: 'online',
            capabilities: ['on_off', 'brightness'],
            location: 'unknown',
            ip_address: ip,
            last_seen: new Date(),
            attributes: {
              brightness: 100,
              color: '#ffffff'
            }
          };
          devices.push(device);
        } catch (error) {
          // Device not reachable
        }
      }

      console.log(`📶 Discovered ${devices.length} Wi-Fi devices`);
      return devices;

    } catch (error) {
      console.error('❌ Wi-Fi discovery error:', error);
      return [];
    }
  }

  private async discoverBluetoothDevices(timeout: number): Promise<IoTDevice[]> {
    const devices: IoTDevice[] = [];

    try {
      // Simulate Bluetooth device discovery
      // In a real implementation, you would use 'noble' or similar Bluetooth library
      const simulatedBluetoothDevices = [
        {
          id: 'bt_speaker_001',
          name: 'Smart Speaker',
          type: 'speaker' as const,
          mac: '00:11:22:33:44:55'
        },
        {
          id: 'bt_lock_001',
          name: 'Smart Lock',
          type: 'lock' as const,
          mac: '00:11:22:33:44:56'
        }
      ];

      for (const btDevice of simulatedBluetoothDevices) {
        const device: IoTDevice = {
          id: btDevice.id,
          name: btDevice.name,
          type: btDevice.type,
          protocol: 'bluetooth',
          status: 'online',
          capabilities: btDevice.type === 'speaker' ? ['play', 'pause', 'volume'] : ['lock', 'unlock'],
          location: 'unknown',
          mac_address: btDevice.mac,
          last_seen: new Date(),
          attributes: {}
        };
        devices.push(device);
      }

      console.log(`📱 Discovered ${devices.length} Bluetooth devices`);
      return devices;

    } catch (error) {
      console.error('❌ Bluetooth discovery error:', error);
      return [];
    }
  }

  private async discoverHomeAssistantDevices(): Promise<IoTDevice[]> {
    const devices: IoTDevice[] = [];

    try {
      if (!this.homeAssistantConfig.enabled || !this.homeAssistantConfig.token) {
        return devices;
      }

      const response = await axios.get(`${this.homeAssistantConfig.url}/api/states`, {
        headers: {
          'Authorization': `Bearer ${this.homeAssistantConfig.token}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });

      for (const entity of response.data) {
        if (entity.entity_id.startsWith('light.') ||
            entity.entity_id.startsWith('switch.') ||
            entity.entity_id.startsWith('camera.') ||
            entity.entity_id.startsWith('lock.')) {

          const device: IoTDevice = {
            id: entity.entity_id,
            name: entity.attributes.friendly_name || entity.entity_id,
            type: this.mapHomeAssistantType(entity.entity_id),
            protocol: 'wifi',
            status: entity.state === 'unavailable' ? 'offline' : 'online',
            capabilities: this.getHomeAssistantCapabilities(entity),
            location: entity.attributes.area_id || 'unknown',
            last_seen: new Date(entity.last_changed),
            attributes: entity.attributes
          };
          devices.push(device);
        }
      }

      console.log(`🏠 Discovered ${devices.length} Home Assistant devices`);
      return devices;

    } catch (error) {
      console.error('❌ Home Assistant discovery error:', error);
      return [];
    }
  }

  private mapHomeAssistantType(entityId: string): IoTDevice['type'] {
    if (entityId.startsWith('light.')) return 'light';
    if (entityId.startsWith('switch.')) return 'switch';
    if (entityId.startsWith('camera.')) return 'camera';
    if (entityId.startsWith('lock.')) return 'lock';
    if (entityId.startsWith('climate.')) return 'thermostat';
    return 'switch';
  }

  private getHomeAssistantCapabilities(entity: any): string[] {
    const capabilities: string[] = ['on_off'];

    if (entity.attributes.brightness !== undefined) capabilities.push('brightness');
    if (entity.attributes.color_mode) capabilities.push('color');
    if (entity.attributes.temperature) capabilities.push('temperature');
    if (entity.attributes.volume_level !== undefined) capabilities.push('volume');

    return capabilities;
  }

  private async controlDevice(deviceId: string, action: string, value?: any): Promise<any> {
    try {
      const device = this.devices.get(deviceId) || this.findDeviceByName(deviceId);

      if (!device) {
        return {
          success: false,
          error: `Device '${deviceId}' not found`
        };
      }

      let result;

      // Route to appropriate control method based on protocol
      switch (device.protocol) {
        case 'wifi':
          if (device.id.startsWith('light.') || device.id.startsWith('switch.')) {
            result = await this.controlHomeAssistantDevice(device, action, value);
          } else {
            result = await this.controlWiFiDevice(device, action, value);
          }
          break;

        case 'bluetooth':
          result = await this.controlBluetoothDevice(device, action, value);
          break;

        default:
          result = await this.controlGenericDevice(device, action, value);
      }

      // Update device status
      if (result.success) {
        device.last_seen = new Date();
        device.status = 'online';
        this.devices.set(device.id, device);
      }

      return result;

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Control failed'
      };
    }
  }

  private async controlHomeAssistantDevice(device: IoTDevice, action: string, value?: any): Promise<any> {
    try {
      if (!this.homeAssistantConfig.enabled) {
        throw new Error('Home Assistant not configured');
      }

      let service: string;
      let serviceData: any = { entity_id: device.id };

      switch (action.toLowerCase()) {
        case 'on':
        case 'turn_on':
          service = device.type === 'light' ? 'light/turn_on' : 'switch/turn_on';
          if (value && device.type === 'light') {
            if (typeof value === 'number') serviceData.brightness = value;
            if (typeof value === 'string' && value.startsWith('#')) serviceData.color_name = value;
          }
          break;

        case 'off':
        case 'turn_off':
          service = device.type === 'light' ? 'light/turn_off' : 'switch/turn_off';
          break;

        case 'toggle':
          service = device.type === 'light' ? 'light/toggle' : 'switch/toggle';
          break;

        case 'set_brightness':
          if (device.type === 'light') {
            service = 'light/turn_on';
            serviceData.brightness = parseInt(value) || 255;
          } else {
            throw new Error('Brightness not supported for this device');
          }
          break;

        default:
          throw new Error(`Unsupported action: ${action}`);
      }

      const response = await axios.post(
        `${this.homeAssistantConfig.url}/api/services/${service}`,
        serviceData,
        {
          headers: {
            'Authorization': `Bearer ${this.homeAssistantConfig.token}`,
            'Content-Type': 'application/json'
          },
          timeout: 5000
        }
      );

      return {
        success: true,
        action,
        device: device.name,
        result: response.data
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Home Assistant control failed'
      };
    }
  }

  private async controlWiFiDevice(device: IoTDevice, action: string, value?: any): Promise<any> {
    try {
      // Simulate Wi-Fi device control
      // In a real implementation, you would send HTTP requests to the device's API

      const supportedActions = ['on', 'off', 'toggle', 'set_brightness', 'set_color'];
      if (!supportedActions.includes(action.toLowerCase())) {
        throw new Error(`Unsupported action: ${action}`);
      }

      // Update device attributes based on action
      switch (action.toLowerCase()) {
        case 'on':
          device.attributes.state = 'on';
          break;
        case 'off':
          device.attributes.state = 'off';
          break;
        case 'toggle':
          device.attributes.state = device.attributes.state === 'on' ? 'off' : 'on';
          break;
        case 'set_brightness':
          device.attributes.brightness = parseInt(value) || 100;
          break;
        case 'set_color':
          device.attributes.color = value || '#ffffff';
          break;
      }

      // Publish MQTT message if available
      if (this.mqttClient) {
        const topic = `homeassistant/${device.type}/${device.id}/set`;
        const payload = JSON.stringify({ [action]: value || true });
        this.mqttClient.publish(topic, payload);
      }

      return {
        success: true,
        action,
        device: device.name,
        new_state: device.attributes
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Wi-Fi device control failed'
      };
    }
  }

  private async controlBluetoothDevice(device: IoTDevice, action: string, value?: any): Promise<any> {
    try {
      // Simulate Bluetooth device control
      // In a real implementation, you would use Bluetooth libraries to send commands

      const supportedActions = device.type === 'speaker'
        ? ['play', 'pause', 'stop', 'volume_up', 'volume_down', 'set_volume']
        : ['lock', 'unlock'];

      if (!supportedActions.includes(action.toLowerCase())) {
        throw new Error(`Unsupported action: ${action} for ${device.type}`);
      }

      // Update device state
      switch (action.toLowerCase()) {
        case 'play':
          device.attributes.playing = true;
          break;
        case 'pause':
        case 'stop':
          device.attributes.playing = false;
          break;
        case 'set_volume':
          device.attributes.volume = parseInt(value) || 50;
          break;
        case 'lock':
          device.attributes.locked = true;
          break;
        case 'unlock':
          device.attributes.locked = false;
          break;
      }

      return {
        success: true,
        action,
        device: device.name,
        new_state: device.attributes
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Bluetooth device control failed'
      };
    }
  }

  private async controlGenericDevice(device: IoTDevice, action: string, value?: any): Promise<any> {
    // Fallback for unknown protocols
    return {
      success: false,
      error: `Protocol '${device.protocol}' not supported`
    };
  }

  private async getDeviceStatus(deviceId: string): Promise<any> {
    try {
      const device = this.devices.get(deviceId) || this.findDeviceByName(deviceId);

      if (!device) {
        return {
          success: false,
          error: `Device '${deviceId}' not found`
        };
      }

      return {
        success: true,
        device: {
          id: device.id,
          name: device.name,
          type: device.type,
          protocol: device.protocol,
          status: device.status,
          capabilities: device.capabilities,
          location: device.location,
          last_seen: device.last_seen,
          attributes: device.attributes
        }
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get device status'
      };
    }
  }

  private async listDevices(type?: string, location?: string, status?: string): Promise<any> {
    try {
      let filteredDevices = Array.from(this.devices.values());

      if (type) {
        filteredDevices = filteredDevices.filter(d => d.type === type);
      }

      if (location) {
        filteredDevices = filteredDevices.filter(d => d.location === location);
      }

      if (status) {
        filteredDevices = filteredDevices.filter(d => d.status === status);
      }

      return {
        success: true,
        total_devices: this.devices.size,
        filtered_count: filteredDevices.length,
        devices: filteredDevices.map(d => ({
          id: d.id,
          name: d.name,
          type: d.type,
          protocol: d.protocol,
          status: d.status,
          location: d.location,
          capabilities: d.capabilities
        }))
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to list devices'
      };
    }
  }

  private async controlScene(sceneName: string, parameters?: any): Promise<any> {
    try {
      // Predefined scenes
      const scenes: Record<string, any> = {
        'movie_night': {
          actions: [
            { device: 'living_room_lights', action: 'set_brightness', value: 20 },
            { device: 'tv', action: 'on' },
            { device: 'sound_system', action: 'on' }
          ]
        },
        'bedtime': {
          actions: [
            { device: 'all_lights', action: 'off' },
            { device: 'front_door', action: 'lock' },
            { device: 'thermostat', action: 'set_temperature', value: 68 }
          ]
        },
        'wake_up': {
          actions: [
            { device: 'bedroom_lights', action: 'set_brightness', value: 80 },
            { device: 'coffee_maker', action: 'on' },
            { device: 'thermostat', action: 'set_temperature', value: 72 }
          ]
        },
        'away': {
          actions: [
            { device: 'all_lights', action: 'off' },
            { device: 'all_locks', action: 'lock' },
            { device: 'security_system', action: 'arm' }
          ]
        }
      };

      const scene = scenes[sceneName.toLowerCase().replace(/\s+/g, '_')];
      if (!scene) {
        return {
          success: false,
          error: `Scene '${sceneName}' not found`,
          available_scenes: Object.keys(scenes)
        };
      }

      const results: any[] = [];
      for (const sceneAction of scene.actions) {
        const result = await this.controlDevice(sceneAction.device, sceneAction.action, sceneAction.value);
        results.push(result);
      }

      return {
        success: true,
        scene: sceneName,
        actions_executed: results.length,
        results
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Scene control failed'
      };
    }
  }

  private async processVoiceCommand(command: string, speaker?: string): Promise<any> {
    try {
      const normalizedCommand = command.toLowerCase().trim();

      // Parse natural language commands
      const commandPatterns = [
        {
          pattern: /turn (on|off) (.+)/,
          action: (matches: RegExpMatchArray) => ({
            action: matches[1] === 'on' ? 'on' : 'off',
            device: matches[2]
          })
        },
        {
          pattern: /(dim|brighten) (.+)/,
          action: (matches: RegExpMatchArray) => ({
            action: 'set_brightness',
            device: matches[2],
            value: matches[1] === 'dim' ? 30 : 80
          })
        },
        {
          pattern: /set (.+) to (\d+)%?/,
          action: (matches: RegExpMatchArray) => ({
            action: 'set_brightness',
            device: matches[1],
            value: parseInt(matches[2]) * 2.55 // Convert percentage to 0-255
          })
        },
        {
          pattern: /(lock|unlock) (.+)/,
          action: (matches: RegExpMatchArray) => ({
            action: matches[1],
            device: matches[2]
          })
        },
        {
          pattern: /activate (.+) scene/,
          action: (matches: RegExpMatchArray) => ({
            action: 'scene',
            scene: matches[1]
          })
        }
      ];

      for (const pattern of commandPatterns) {
        const matches = normalizedCommand.match(pattern.pattern);
        if (matches) {
          const parsed = pattern.action(matches);

          if (parsed.action === 'scene') {
            return await this.controlScene((parsed as any).scene);
          } else {
            return await this.controlDevice((parsed as any).device, parsed.action, (parsed as any).value);
          }
        }
      }

      // If no pattern matches, try to find device by name and toggle
      const deviceNames = Array.from(this.devices.values()).map(d => d.name.toLowerCase());
      const mentionedDevice = deviceNames.find(name => normalizedCommand.includes(name));

      if (mentionedDevice) {
        const device = Array.from(this.devices.values()).find(d => d.name.toLowerCase() === mentionedDevice);
        if (device) {
          return await this.controlDevice(device.id, 'toggle');
        }
      }

      return {
        success: false,
        error: `Could not understand command: "${command}"`,
        suggestion: 'Try commands like "turn on living room lights" or "activate movie night scene"'
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Voice command processing failed'
      };
    }
  }

  private findDeviceByName(name: string): IoTDevice | undefined {
    const normalizedName = name.toLowerCase();
    return Array.from(this.devices.values()).find(device =>
      device.name.toLowerCase().includes(normalizedName) ||
      device.id.toLowerCase().includes(normalizedName)
    );
  }

  private async loadHomeAssistantDevices() {
    try {
      const devices = await this.discoverHomeAssistantDevices();
      devices.forEach(device => {
        this.devices.set(device.id, device);
      });
      console.log(`🏠 Loaded ${devices.length} Home Assistant devices`);
    } catch (error) {
      console.error('❌ Failed to load Home Assistant devices:', error);
    }
  }

  private async scanBluetoothDevices() {
    try {
      // Simulate Bluetooth scanning
      const devices = await this.discoverBluetoothDevices(10);
      devices.forEach(device => {
        this.devices.set(device.id, device);
      });
    } catch (error) {
      console.error('❌ Bluetooth scan error:', error);
    }
  }

  private handleMQTTMessage(topic: string, message: string) {
    try {
      const data = JSON.parse(message);

      // Handle different MQTT topic patterns
      if (topic.startsWith('homeassistant/')) {
        this.handleHomeAssistantMQTT(topic, data);
      } else if (topic.startsWith('zigbee2mqtt/')) {
        this.handleZigbee2MQTT(topic, data);
      } else if (topic.startsWith('tasmota/')) {
        this.handleTasmotaMQTT(topic, data);
      }

    } catch (error) {
      console.error('❌ MQTT message parsing error:', error);
    }
  }

  private handleHomeAssistantMQTT(topic: string, data: any) {
    // Extract entity ID from topic
    const parts = topic.split('/');
    if (parts.length >= 3) {
      const entityId = `${parts[1]}.${parts[2]}`;
      const device = this.devices.get(entityId);

      if (device) {
        device.attributes = { ...device.attributes, ...data };
        device.last_seen = new Date();
        this.devices.set(entityId, device);
      }
    }
  }

  private handleZigbee2MQTT(topic: string, data: any) {
    // Handle Zigbee2MQTT device updates
    const deviceName = topic.split('/')[1];
    const deviceId = `zigbee_${deviceName}`;

    let device = this.devices.get(deviceId);
    if (!device) {
      // Create new Zigbee device
      device = {
        id: deviceId,
        name: deviceName,
        type: this.inferDeviceType(data),
        protocol: 'zigbee',
        status: 'online',
        capabilities: this.inferCapabilities(data),
        location: 'unknown',
        last_seen: new Date(),
        attributes: data
      };
    } else {
      device.attributes = { ...device.attributes, ...data };
      device.last_seen = new Date();
    }

    this.devices.set(deviceId, device);
  }

  private handleTasmotaMQTT(topic: string, data: any) {
    // Handle Tasmota device updates
    const deviceName = topic.split('/')[1];
    const deviceId = `tasmota_${deviceName}`;

    let device = this.devices.get(deviceId);
    if (!device) {
      device = {
        id: deviceId,
        name: deviceName,
        type: 'switch',
        protocol: 'wifi',
        status: 'online',
        capabilities: ['on_off'],
        location: 'unknown',
        last_seen: new Date(),
        attributes: data
      };
    } else {
      device.attributes = { ...device.attributes, ...data };
      device.last_seen = new Date();
    }

    this.devices.set(deviceId, device);
  }

  private inferDeviceType(data: any): IoTDevice['type'] {
    if (data.brightness !== undefined || data.color !== undefined) return 'light';
    if (data.temperature !== undefined) return 'sensor';
    if (data.contact !== undefined) return 'sensor';
    if (data.motion !== undefined) return 'sensor';
    return 'switch';
  }

  private inferCapabilities(data: any): string[] {
    const capabilities: string[] = [];

    if (data.state !== undefined) capabilities.push('on_off');
    if (data.brightness !== undefined) capabilities.push('brightness');
    if (data.color !== undefined) capabilities.push('color');
    if (data.temperature !== undefined) capabilities.push('temperature');
    if (data.humidity !== undefined) capabilities.push('humidity');

    return capabilities;
  }

  protected override async getResourceContent(resource: any): Promise<any> {
    switch (resource.uri) {
      case 'iot://devices':
        return Array.from(this.devices.values());

      case 'iot://device-types':
        return {
          supported_types: ['light', 'camera', 'sensor', 'lock', 'speaker', 'thermostat', 'switch', 'outlet'],
          protocols: ['wifi', 'bluetooth', 'zigbee', 'zwave', 'matter'],
          capabilities: ['on_off', 'brightness', 'color', 'temperature', 'volume', 'lock', 'unlock']
        };

      case 'iot://network-status':
        return {
          mqtt_connected: this.mqttClient?.connected || false,
          home_assistant_connected: this.homeAssistantConfig.enabled,
          bluetooth_enabled: this.bluetoothConfig.enabled,
          total_devices: this.devices.size,
          online_devices: Array.from(this.devices.values()).filter(d => d.status === 'online').length
        };

      default:
        return null;
    }
  }

  override async stop(): Promise<void> {
    console.log('🔌 Shutting down IoT Controller...');

    if (this.deviceDiscoveryInterval) {
      clearInterval(this.deviceDiscoveryInterval);
    }

    if (this.mqttClient) {
      this.mqttClient.end();
    }

    await super.stop();
  }
}

// Export the server class and start function
export { IoTControllerMCPServer };

export async function startIoTControllerServer(port: number = 3010): Promise<IoTControllerMCPServer> {
  const server = new IoTControllerMCPServer(port);
  await server.start();
  console.log(`🏠 IoT Controller MCP Server started on port ${port}`);
  return server;
}

// Start server if this file is run directly
if (require.main === module) {
  startIoTControllerServer().catch(console.error);
}
