from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By

class ChromeControlMCP:
    def __init__(self, headless=True):
        options = Options()
        if headless:
            options.add_argument("--headless=new")
        self.driver = webdriver.Chrome(options=options)

    def open_url(self, url: str):
        self.driver.get(url)
        return {"status": "opened", "url": url, "title": self.driver.title}

    def get_elements_by_tag(self, tag_name: str):
        elements = self.driver.find_elements(By.TAG_NAME, tag_name)
        return {"count": len(elements), "tags": [e.text for e in elements[:5]]}

    def close_browser(self):
        self.driver.quit()
        return {"status": "closed"}
