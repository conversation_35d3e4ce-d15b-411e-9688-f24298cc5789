import subprocess
import os

class GitMCP:
    def __init__(self, repo_path="."):
        self.repo_path = os.path.abspath(repo_path)

    def run_git_command(self, command: str):
        try:
            result = subprocess.run(
                ["git"] + command.split(),
                cwd=self.repo_path,
                capture_output=True,
                text=True
            )
            return {
                "stdout": result.stdout.strip(),
                "stderr": result.stderr.strip(),
                "returncode": result.returncode
            }
        except Exception as e:
            return {"error": str(e)}

    def get_status(self):
        return self.run_git_command("status")

    def pull(self):
        return self.run_git_command("pull")

    def push(self):
        return self.run_git_command("push")

    def add_commit_push(self, commit_msg: str):
        self.run_git_command("add .")
        self.run_git_command(f'commit -m "{commit_msg}"')
        return self.push()
