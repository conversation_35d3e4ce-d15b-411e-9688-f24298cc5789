import requests
import json

class AgentLeeBridgeMCP:
    def __init__(self, endpoint_url="http://localhost:8000/agentlee/bridge"):
        self.endpoint_url = endpoint_url

    def send_message_to_agent(self, message: str):
        payload = {"message": message}
        headers = {"Content-Type": "application/json"}
        response = requests.post(self.endpoint_url, data=json.dumps(payload), headers=headers)
        response.raise_for_status()
        return response.json()

    def relay_command(self, command: str):
        return self.send_message_to_agent(f"COMMAND::{command}")
