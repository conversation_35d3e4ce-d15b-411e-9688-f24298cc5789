import React from 'react';

interface LiveKitControllerProps {
  onMessage: (message: string) => void;
}

export const LiveKitController: React.FC<LiveKitControllerProps> = ({ onMessage }) => {
  // Placeholder for LiveKit integration
  // You can add real-time voice/video logic here
  return (
    <div className="flex flex-col items-center justify-center h-full">
      <div className="text-gray-400 mb-2">LiveKit connection will appear here.</div>
      <button
        className="px-4 py-2 bg-blue-600 text-white rounded-lg shadow hover:bg-blue-700"
        onClick={() => onMessage('Test message from LiveKit')}
      >
        Send Test Message
      </button>
    </div>
  );
};
