/**
 * Chrome Control MCP Server with IoT Integration
 * Enables browser automation triggered by IoT events and voice commands
 */

import { MCPServer, MCPTool, MCPResource, createToolSchema, ToolPropertyTypes } from '../mcp-base';
import axios from 'axios';
import { spawn } from 'child_process';

interface BrowserAction {
  type: 'open_url' | 'navigate' | 'screenshot' | 'execute_script' | 'close_tab';
  url?: string;
  script?: string;
  tabId?: string;
}

interface IoTBrowserTrigger {
  deviceId: string;
  condition: string;
  action: BrowserAction;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
}

class ChromeControlMCPServer extends MCPServer {
  private iotControllerUrl: string;
  private chromeDebugPort: number;
  private activeTriggers: Map<string, IoTBrowserTrigger> = new Map();
  private openTabs: Map<string, any> = new Map();

  constructor() {
    super({
      name: 'Chrome Control MCP Server',
      version: '1.0.0',
      port: 3015
    });

    this.iotControllerUrl = 'http://localhost:3010';
    this.chromeDebugPort = 9222;
    this.setupTools();
    this.setupResources();
  }

  private setupTools(): void {
    // Open URL Tool
    const openUrlTool: MCPTool = {
      name: 'openUrl',
      description: 'Open a URL in Chrome browser',
      inputSchema: createToolSchema({
        url: ToolPropertyTypes.string('URL to open'),
        newTab: ToolPropertyTypes.boolean('Open in new tab', true),
        speaker: ToolPropertyTypes.string('Speaker/user identifier')
      }, ['url']),
      handler: async (params: any) => {
        return await this.openUrl(params.url, params.newTab, params.speaker);
      }
    };

    // IoT-Triggered Browser Action Tool
    const iotBrowserActionTool: MCPTool = {
      name: 'iotBrowserAction',
      description: 'Execute browser action based on IoT device trigger',
      inputSchema: createToolSchema({
        deviceId: ToolPropertyTypes.string('IoT device ID that triggered the action'),
        action: ToolPropertyTypes.string('Browser action to execute (open_url, navigate, screenshot)'),
        url: ToolPropertyTypes.string('URL for browser action'),
        script: ToolPropertyTypes.string('JavaScript to execute'),
        priority: ToolPropertyTypes.string('Action priority (low, normal, high, urgent)', 'normal')
      }, ['deviceId', 'action']),
      handler: async (params: any) => {
        return await this.executeIoTBrowserAction(params);
      }
    };

    // Setup IoT Browser Trigger Tool
    const setupIoTBrowserTriggerTool: MCPTool = {
      name: 'setupIoTBrowserTrigger',
      description: 'Setup browser action to be triggered by IoT device events',
      inputSchema: createToolSchema({
        deviceId: ToolPropertyTypes.string('IoT device ID to monitor'),
        condition: ToolPropertyTypes.string('Trigger condition (motion_detected, door_opened, etc.)'),
        browserAction: ToolPropertyTypes.string('Browser action to execute (open_url, navigate, screenshot)'),
        url: ToolPropertyTypes.string('URL for the browser action'),
        script: ToolPropertyTypes.string('JavaScript code to execute'),
        speaker: ToolPropertyTypes.string('Speaker/user identifier')
      }, ['deviceId', 'condition', 'browserAction']),
      handler: async (params: any) => {
        return await this.setupIoTBrowserTrigger(params);
      }
    };

    // Process Voice Command Tool
    const processVoiceCommandTool: MCPTool = {
      name: 'processVoiceCommand',
      description: 'Process natural language voice commands for browser control with IoT context',
      inputSchema: createToolSchema({
        command: ToolPropertyTypes.string('Natural language command'),
        speaker: ToolPropertyTypes.string('Speaker/user identifier'),
        iotContext: ToolPropertyTypes.object('Current IoT device states and context', {})
      }, ['command']),
      handler: async (params: any) => {
        return await this.processVoiceCommand(params.command, params.speaker, params.iotContext);
      }
    };

    // Take Screenshot Tool
    const takeScreenshotTool: MCPTool = {
      name: 'takeScreenshot',
      description: 'Take a screenshot of the current browser tab',
      inputSchema: createToolSchema({
        tabId: ToolPropertyTypes.string('Tab ID (optional, uses active tab if not specified)'),
        speaker: ToolPropertyTypes.string('Speaker/user identifier')
      }),
      handler: async (params: any) => {
        return await this.takeScreenshot(params.tabId, params.speaker);
      }
    };

    // Execute JavaScript Tool
    const executeScriptTool: MCPTool = {
      name: 'executeScript',
      description: 'Execute JavaScript in the browser',
      inputSchema: createToolSchema({
        script: ToolPropertyTypes.string('JavaScript code to execute'),
        tabId: ToolPropertyTypes.string('Tab ID (optional, uses active tab if not specified)'),
        speaker: ToolPropertyTypes.string('Speaker/user identifier')
      }, ['script']),
      handler: async (params: any) => {
        return await this.executeScript(params.script, params.tabId, params.speaker);
      }
    };

    // Register all tools
    this.addTool(openUrlTool);
    this.addTool(iotBrowserActionTool);
    this.addTool(setupIoTBrowserTriggerTool);
    this.addTool(processVoiceCommandTool);
    this.addTool(takeScreenshotTool);
    this.addTool(executeScriptTool);
  }

  private setupResources(): void {
    // Active Browser Triggers Resource
    const triggersResource: MCPResource = {
      uri: 'chrome://triggers',
      name: 'Active IoT Browser Triggers',
      description: 'List of active IoT triggers that can execute browser actions',
      mimeType: 'application/json'
    };

    // Open Tabs Resource
    const tabsResource: MCPResource = {
      uri: 'chrome://tabs',
      name: 'Open Browser Tabs',
      description: 'List of currently open browser tabs',
      mimeType: 'application/json'
    };

    // Browser Status Resource
    const statusResource: MCPResource = {
      uri: 'chrome://status',
      name: 'Chrome Control Status',
      description: 'Current status of Chrome control service',
      mimeType: 'application/json'
    };

    this.addResource(triggersResource);
    this.addResource(tabsResource);
    this.addResource(statusResource);
  }

  private async openUrl(url: string, newTab: boolean = true, speaker?: string): Promise<any> {
    try {
      // Use Chrome DevTools Protocol to open URL
      const response = await axios.post(`http://localhost:${this.chromeDebugPort}/json/new?${encodeURIComponent(url)}`);

      if (response.data && response.data.id) {
        const tabId = response.data.id;
        this.openTabs.set(tabId, {
          id: tabId,
          url,
          title: response.data.title || 'Loading...',
          openedBy: speaker,
          openedAt: new Date().toISOString()
        });

        return {
          success: true,
          action: 'url_opened',
          url,
          tabId,
          speaker,
          timestamp: new Date().toISOString()
        };
      } else {
        throw new Error('Failed to get tab ID from Chrome');
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to open URL: ${error instanceof Error ? error.message : 'Unknown error'}`,
        url,
        speaker
      };
    }
  }

  private async executeIoTBrowserAction(params: any): Promise<any> {
    const { deviceId, action, url, script, priority = 'normal' } = params;

    try {
      // Get device status from IoT controller
      const deviceResponse = await axios.get(`${this.iotControllerUrl}/devices/${deviceId}`);
      const device = deviceResponse.data;

      let result: any;
      switch (action) {
        case 'open_url':
          if (!url) throw new Error('URL required for open_url action');
          result = await this.openUrl(url, true, `IoT-${deviceId}`);
          break;

        case 'navigate':
          if (!url) throw new Error('URL required for navigate action');
          result = await this.navigateToUrl(url);
          break;

        case 'screenshot':
          result = await this.takeScreenshot(undefined, `IoT-${deviceId}`);
          break;

        case 'execute_script':
          if (!script) throw new Error('Script required for execute_script action');
          result = await this.executeScript(script, undefined, `IoT-${deviceId}`);
          break;

        default:
          throw new Error(`Unsupported browser action: ${action}`);
      }

      return {
        success: true,
        deviceId,
        deviceName: device.name,
        action,
        priority,
        result,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        success: false,
        error: `IoT browser action failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        deviceId,
        action
      };
    }
  }

  private async setupIoTBrowserTrigger(params: any): Promise<any> {
    const { deviceId, condition, browserAction, url, script, speaker } = params;

    const trigger: IoTBrowserTrigger = {
      deviceId,
      condition,
      action: {
        type: browserAction as any,
        url,
        script
      },
      priority: 'normal'
    };

    const triggerId = `${deviceId}-${condition}-${browserAction}`;
    this.activeTriggers.set(triggerId, trigger);

    // Register trigger with IoT controller
    try {
      await axios.post(`${this.iotControllerUrl}/triggers`, {
        id: triggerId,
        deviceId,
        condition,
        callback: `${this.config.name}/iotBrowserAction`,
        params: {
          deviceId,
          action: browserAction,
          url,
          script
        }
      });

      return {
        success: true,
        triggerId,
        trigger,
        message: `IoT browser trigger setup successfully`,
        speaker
      };
    } catch (error) {
      this.activeTriggers.delete(triggerId);
      return {
        success: false,
        error: `Failed to setup IoT trigger: ${error instanceof Error ? error.message : 'Unknown error'}`,
        triggerId
      };
    }
  }

  private async navigateToUrl(url: string, tabId?: string): Promise<any> {
    try {
      // If no tabId specified, use the first available tab
      if (!tabId) {
        const tabsResponse = await axios.get(`http://localhost:${this.chromeDebugPort}/json`);
        const tabs = tabsResponse.data;
        if (tabs.length > 0) {
          tabId = tabs[0].id;
        } else {
          throw new Error('No tabs available for navigation');
        }
      }

      await axios.post(`http://localhost:${this.chromeDebugPort}/json/runtime/evaluate`, {
        expression: `window.location.href = '${url}'`
      });

      return {
        success: true,
        action: 'navigation_completed',
        url,
        tabId
      };
    } catch (error) {
      return {
        success: false,
        error: `Navigation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        url
      };
    }
  }

  private async takeScreenshot(tabId?: string, speaker?: string): Promise<any> {
    try {
      // Implementation would use Chrome DevTools Protocol to capture screenshot
      // For now, return a simulated response
      return {
        success: true,
        action: 'screenshot_taken',
        tabId: tabId || 'active',
        speaker,
        timestamp: new Date().toISOString(),
        message: 'Screenshot functionality not fully implemented yet'
      };
    } catch (error) {
      return {
        success: false,
        error: `Screenshot failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        tabId,
        speaker
      };
    }
  }

  private async executeScript(script: string, tabId?: string, speaker?: string): Promise<any> {
    try {
      // Use Chrome DevTools Protocol to execute JavaScript
      const response = await axios.post(`http://localhost:${this.chromeDebugPort}/json/runtime/evaluate`, {
        expression: script,
        returnByValue: true
      });

      return {
        success: true,
        action: 'script_executed',
        script,
        result: response.data.result,
        tabId: tabId || 'active',
        speaker,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        success: false,
        error: `Script execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        script,
        speaker
      };
    }
  }

  private async processVoiceCommand(command: string, speaker?: string, iotContext?: any): Promise<any> {
    const lowerCommand = command.toLowerCase();

    // Browser command patterns with IoT context
    const patterns = [
      {
        pattern: /open (.+) when (.+) (is|are) (.+)/,
        action: (matches: RegExpMatchArray) => ({
          action: 'setup_trigger',
          url: matches[1],
          condition: `${matches[2]}_${matches[4]}`,
          browserAction: 'open_url'
        })
      },
      {
        pattern: /show (?:security )?camera (?:feed )?when (.+) detected/,
        action: (matches: RegExpMatchArray) => ({
          action: 'setup_trigger',
          url: 'http://camera.local/feed',
          condition: `${matches[1]}_detected`,
          browserAction: 'open_url'
        })
      },
      {
        pattern: /open (.+)/,
        action: (matches: RegExpMatchArray) => ({
          action: 'open_url',
          url: matches[1]
        })
      },
      {
        pattern: /take screenshot/,
        action: () => ({
          action: 'screenshot'
        })
      }
    ];

    // Check all patterns
    for (const pattern of patterns) {
      const matches = lowerCommand.match(pattern.pattern);
      if (matches) {
        const parsed = pattern.action(matches);

        if (parsed.action === 'setup_trigger') {
          return await this.setupIoTBrowserTrigger({
            deviceId: 'auto-detected',
            condition: (parsed as any).condition,
            browserAction: (parsed as any).browserAction,
            url: (parsed as any).url,
            speaker
          });
        } else if (parsed.action === 'open_url') {
          return await this.openUrl((parsed as any).url, true, speaker);
        } else if (parsed.action === 'screenshot') {
          return await this.takeScreenshot(undefined, speaker);
        }
      }
    }

    return {
      success: false,
      error: 'Could not understand browser command',
      command,
      suggestion: 'Try commands like "open google.com" or "show camera when motion detected"'
    };
  }

  protected override async getResourceContent(resource: MCPResource): Promise<any> {
    switch (resource.uri) {
      case 'chrome://triggers':
        return {
          triggers: Array.from(this.activeTriggers.entries()).map(([id, trigger]) => ({
            id,
            ...trigger
          })),
          count: this.activeTriggers.size
        };

      case 'chrome://tabs':
        return {
          tabs: Array.from(this.openTabs.values()),
          count: this.openTabs.size
        };

      case 'chrome://status':
        return {
          service: 'Chrome Control MCP',
          status: 'active',
          chromeDebugPort: this.chromeDebugPort,
          iotControllerUrl: this.iotControllerUrl,
          activeTriggers: this.activeTriggers.size,
          openTabs: this.openTabs.size,
          timestamp: new Date().toISOString()
        };

      default:
        return { error: 'Resource not found' };
    }
  }
}

// Export for use as module
export default ChromeControlMCPServer;

// CLI support
if (require.main === module) {
  const server = new ChromeControlMCPServer();
  server.start().then(() => {
    console.log('🌐 Chrome Control MCP Server with IoT integration started');
  }).catch(console.error);
}
