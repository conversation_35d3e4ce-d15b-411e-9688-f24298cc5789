import requests

class GitHubMCP:
    def __init__(self, token=None):
        self.base_url = "https://api.github.com"
        self.headers = {"Authorization": f"token {token}"} if token else {}

    def get_user_repos(self, username):
        url = f"{self.base_url}/users/{username}/repos"
        response = requests.get(url, headers=self.headers)
        response.raise_for_status()
        return response.json()

    def create_repo(self, name, private=True):
        url = f"{self.base_url}/user/repos"
        payload = {"name": name, "private": private}
        response = requests.post(url, headers=self.headers, json=payload)
        response.raise_for_status()
        return response.json()
