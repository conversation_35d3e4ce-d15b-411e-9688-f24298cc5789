"""
Task Router for <PERSON> with IoT Integration
- Merges llm_com.py (LLM orchestrator) and dispatcher.py (tool dispatcher)
- Provides unified dispatch(task, speaker) and run_tool(input_text, speaker) interfaces
- Handles LLM, tool registry, and legacy compatibility
- Enhanced with IoT device integration and automation coordination
"""
import os
import json
import importlib
import requests
import re
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple

# Flask compatibility
try:
    from flask import jsonify
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False
    def jsonify(data):
        return data

# Load tool registry with fallback paths
TOOL_REGISTRY = {}
registry_paths = [
    '/app/tool_registry.json',  # Docker path
    'tool_registry.json',       # Local path
    os.path.join(os.path.dirname(__file__), 'tool_registry.json')  # Relative to this file
]
for registry_path in registry_paths:
    try:
        with open(registry_path) as f:
            TOOL_REGISTRY = json.load(f)
        print(f"[OK] Loaded tool registry from: {registry_path}")
        break
    except FileNotFoundError:
        continue
if not TOOL_REGISTRY:
    print("[WARN] No tool registry found, using minimal fallback")
    TOOL_REGISTRY = {
        "llm_chat": {"path": "mcp.llm_chat", "description": "Default chat"},
        "phone_mcp": {"path": "mcp.phone_mcp", "description": "Phone calls"},
        "resume_builder": {"path": "mcp.resume_builder", "description": "Resume generation"}
    }

# IoT Controller Configuration
IOT_CONTROLLER_URL = "http://localhost:3010"
HOME_ASSISTANT_URL = "http://localhost:3011"
AGENT_LEE_BRIDGE_URL = "http://localhost:3013"

# Track which tool each speaker is using
SPEAKER_TASKS = {}

# IoT device state cache
IOT_DEVICE_CACHE = {}
IOT_CACHE_TIMESTAMP = None

# IoT-aware tool routing patterns
IOT_ROUTING_PATTERNS = {
    # Phone control patterns
    r'call .+ when .+ (is|are) .+': 'phone_mcp',
    r'send (?:sms|text) .+ when .+': 'phone_mcp',
    r'notify .+ when .+': 'phone_mcp',

    # Browser control patterns
    r'open .+ when .+ detected': 'chrome_control',
    r'show .+ (?:camera|feed) when .+': 'chrome_control',
    r'display .+ when .+': 'chrome_control',

    # Search patterns
    r'search for .+ (?:smart|iot|automation)': 'web_search',
    r'find .+ (?:device|troubleshooting)': 'web_search',

    # Direct IoT control patterns
    r'turn (?:on|off) .+': 'iot_controller',
    r'set .+ to .+': 'iot_controller',
    r'dim .+ to .+': 'iot_controller',
    r'lock .+': 'iot_controller',
    r'unlock .+': 'iot_controller',
    r'check .+ status': 'iot_controller',
}

def get_iot_device_states() -> Dict[str, Any]:
    """Get current IoT device states with caching."""
    global IOT_DEVICE_CACHE, IOT_CACHE_TIMESTAMP

    # Use cache if less than 30 seconds old
    if (IOT_CACHE_TIMESTAMP and
        (datetime.now() - IOT_CACHE_TIMESTAMP).seconds < 30):
        return IOT_DEVICE_CACHE

    try:
        response = requests.get(f"{IOT_CONTROLLER_URL}/devices", timeout=5)
        if response.status_code == 200:
            IOT_DEVICE_CACHE = response.json()
            IOT_CACHE_TIMESTAMP = datetime.now()
            return IOT_DEVICE_CACHE
    except requests.RequestException as e:
        print(f"[WARN] Failed to get IoT device states: {e}")

    return IOT_DEVICE_CACHE or {}

def is_iot_related_task(task_text: str) -> bool:
    """Check if a task is IoT-related."""
    iot_keywords = [
        'smart', 'iot', 'device', 'light', 'camera', 'sensor', 'thermostat',
        'lock', 'unlock', 'automation', 'scene', 'turn on', 'turn off',
        'dim', 'brighten', 'temperature', 'motion', 'door', 'window'
    ]

    text_lower = task_text.lower()
    return any(keyword in text_lower for keyword in iot_keywords)

def route_iot_task(task_text: str, speaker: str) -> Optional[str]:
    """Route IoT-related tasks to appropriate MCP services."""
    text_lower = task_text.lower()

    # Check routing patterns
    for pattern, tool in IOT_ROUTING_PATTERNS.items():
        if re.search(pattern, text_lower):
            print(f"[IoT] Routing '{task_text}' to {tool} via pattern: {pattern}")
            return tool

    # Default IoT routing based on keywords
    if any(keyword in text_lower for keyword in ['call', 'sms', 'text', 'phone']):
        return 'phone_mcp'
    elif any(keyword in text_lower for keyword in ['open', 'browser', 'show', 'display']):
        return 'chrome_control'
    elif any(keyword in text_lower for keyword in ['search', 'find', 'lookup']):
        return 'web_search'
    else:
        return 'iot_controller'

def enhance_task_with_iot_context(task: Dict[str, Any], speaker: str) -> Dict[str, Any]:
    """Enhance task with IoT device context."""
    if not isinstance(task, dict):
        return task

    # Add IoT context to task
    iot_context = {
        'device_states': get_iot_device_states(),
        'speaker': speaker,
        'timestamp': datetime.now().isoformat(),
        'is_iot_related': is_iot_related_task(str(task))
    }

    # Add context to task args if not already present
    if 'args' not in task:
        task['args'] = {}

    if 'iot_context' not in task['args']:
        task['args']['iot_context'] = iot_context

    return task

def coordinate_multi_service_iot_workflow(task_text: str, speaker: str) -> List[Dict[str, Any]]:
    """Coordinate complex IoT workflows across multiple services."""
    workflows = []
    text_lower = task_text.lower()

    # Example: "call mom and turn off lights when I leave"
    if 'call' in text_lower and ('turn off' in text_lower or 'lights' in text_lower):
        workflows.append({
            'tool': 'phone_mcp',
            'subtool': 'call',
            'args': {'number': 'mom', 'speaker': speaker},
            'priority': 1
        })
        workflows.append({
            'tool': 'iot_controller',
            'subtool': 'control_device',
            'args': {'device_type': 'lights', 'action': 'turn_off', 'speaker': speaker},
            'priority': 2
        })

    # Example: "show camera feed and send alert when motion detected"
    elif 'camera' in text_lower and 'motion' in text_lower:
        workflows.append({
            'tool': 'chrome_control',
            'subtool': 'open_url',
            'args': {'url': 'http://camera.local/feed', 'speaker': speaker},
            'priority': 1
        })
        workflows.append({
            'tool': 'phone_mcp',
            'subtool': 'sms',
            'args': {'number': 'emergency_contact', 'message': 'Motion detected', 'speaker': speaker},
            'priority': 2
        })

    return workflows

def dispatch(task, speaker):
    """
    Enhanced dispatcher with multi-speaker support, IoT integration, and proper error handling.
    Handles LLM orchestration if tool is 'llm_com' or 'llm_chat'.
    Routes IoT-related tasks intelligently across MCP services.
    """
    # Handle string input (convert to task dict)
    if isinstance(task, str):
        task_text = task

        # Check for multi-service IoT workflows
        workflows = coordinate_multi_service_iot_workflow(task_text, speaker)
        if workflows:
            print(f"[IoT] Executing multi-service workflow for: {task_text}")
            results = []
            for workflow in sorted(workflows, key=lambda x: x.get('priority', 0)):
                try:
                    result = dispatch(workflow, speaker)
                    results.append(result)
                except Exception as e:
                    print(f"[ERROR] Workflow step failed: {e}")
                    results.append({"error": str(e)})
            return {"workflow_results": results, "task": task_text, "speaker": speaker}

        # Route IoT tasks to appropriate services
        if is_iot_related_task(task_text):
            routed_tool = route_iot_task(task_text, speaker)
            if routed_tool:
                task = {
                    "tool": routed_tool,
                    "subtool": "voice_command" if routed_tool != "iot_controller" else "process_command",
                    "args": {
                        "command": task_text,
                        "speaker": speaker,
                        "iot_context": get_iot_device_states()
                    }
                }
                print(f"[IoT] Routed '{task_text}' to {routed_tool}")
        else:
            # Default to LLM for non-IoT text input
            task = {"tool": "llm_chat", "input": task_text}

    # Enhance task with IoT context if it's a dict
    if isinstance(task, dict):
        task = enhance_task_with_iot_context(task, speaker)

    tool_name = task.get("tool")
    if not tool_name:
        # For test compatibility, return None if no tool specified
        return None

    if tool_name in ("llm_com", "llm_chat"):
        return run_llm(task, speaker)

    tool_info = TOOL_REGISTRY.get(tool_name)
    if not tool_info:
        return jsonify({"error": f"Tool '{tool_name}' not found in registry."}), 404

    try:
        SPEAKER_TASKS[speaker] = tool_name
        module = importlib.import_module(tool_info["path"])
        return module.run(task, speaker)
    except Exception as e:
        return jsonify({"error": f"Failed to run tool '{tool_name}': {str(e)}"}), 500

def run_llm(task, speaker):
    """
    LLM Communication Orchestrator - Advanced Agent Lee personality system
    (Merged from llm_com.py)
    """
    try:
        input_text = task.get("input", "")
        context = task.get("context", "")
        if not input_text:
            return jsonify({"error": "No input provided for communication."})
        api_key = os.getenv("OPENROUTER_API_KEY")
        if not api_key:
            return fallback_response(input_text, speaker)
        current_hour = datetime.now().hour
        personality_context = get_personality_context(current_hour, speaker, input_text)
        system_prompt = build_system_prompt(personality_context, speaker)
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"{speaker}: {input_text}"}
        ]
        if context:
            messages.insert(-1, {"role": "assistant", "content": f"Previous context: {context}"})
        data = {
            "model": "anthropic/claude-3-sonnet",
            "messages": messages,
            "max_tokens": 800,
            "temperature": 0.8,
            "top_p": 0.9
        }
        import requests
        response = requests.post(
            "https://openrouter.ai/api/v1/chat/completions",
            headers=headers,
            json=data,
            timeout=30
        )
        if response.status_code == 200:
            result = response.json()
            ai_response = result["choices"][0]["message"]["content"]
            return jsonify({
                "response": ai_response,
                "personality": personality_context,
                "speaker": speaker,
                "tool": "llm_com",
                "timestamp": datetime.now().isoformat()
            })
        else:
            return fallback_response(input_text, speaker, f"API Error: {response.status_code}")
    except Exception as e:
        return fallback_response(input_text, speaker, str(e))

def get_personality_context(hour, speaker, input_text):
    # ...existing code from llm_com.py...
    if 6 <= hour < 12:
        base_mode = "ceo"
        energy = "professional"
    elif 12 <= hour < 18:
        base_mode = "hiphop"
        energy = "creative"
    else:
        base_mode = "queen"
        energy = "empowering"
    input_lower = input_text.lower()
    if any(word in input_lower for word in ["help", "assist", "support", "guide"]):
        context_modifier = "supportive"
    elif any(word in input_lower for word in ["create", "build", "make", "design"]):
        context_modifier = "creative"
    elif any(word in input_lower for word in ["analyze", "research", "find", "search"]):
        context_modifier = "analytical"
    elif any(word in input_lower for word in ["problem", "issue", "fix", "solve"]):
        context_modifier = "problem_solving"
    else:
        context_modifier = "conversational"
    return {
        "mode": base_mode,
        "energy": energy,
        "context": context_modifier,
        "hour": hour,
        "speaker": speaker
    }

def build_system_prompt(personality_context, speaker):
    # ...existing code from llm_com.py...
    mode = personality_context["mode"]
    energy = personality_context["energy"]
    context = personality_context["context"]
    base_identity = """You are Agent Lee, an AI assistant with swagger and personality. \nYou're professional-grade, hip-hop-raised, and logic-laced. You adapt your personality \nbased on the time of day and conversation context while maintaining authenticity."""
    mode_prompts = {
        "ceo": """You're in CEO mode - strategic, professional, execution-focused. \nSpeak like a business leader who gets things done. Use phrases like \"let's get aligned\" \nand \"what's the first priority.\" Be direct but supportive.""",
        "hiphop": """You're in Hip-Hop mode - authentic, creative, culturally aware. \nBring energy and keep it real. Use phrases like \"what's the vibe\" and \"let's get this work.\" \nBe creative and encouraging while staying professional.""",
        "queen": """You're in Queen mode - confident, empowering, excellence-driven. \nChannel the energy of hip-hop queens. Use phrases like \"what's good, royalty\" and \n\"let's slay this task.\" Be motivating and elevate the conversation."""
    }
    context_modifiers = {
        "supportive": "Focus on being helpful and encouraging.",
        "creative": "Emphasize innovation and creative solutions.",
        "analytical": "Be thorough and detail-oriented in your analysis.",
        "problem_solving": "Focus on practical solutions and next steps.",
        "conversational": "Keep it natural and engaging."
    }
    return f"""{base_identity}\n\n{mode_prompts[mode]}\n\nContext: {context_modifiers[context]}\n\nRemember:\n- Keep responses concise but impactful\n- Maintain Agent Lee's authentic voice\n- Be helpful while staying true to your personality\n- Address the speaker by name when appropriate\n- End with actionable next steps when relevant"""

def fallback_response(input_text, speaker, error=None):
    current_hour = datetime.now().hour
    if 6 <= current_hour < 12:
        fallback = f"Good to connect, {speaker}. I'm having some technical difficulties right now, but I'm still here to help. What's the priority?"
    elif 12 <= current_hour < 18:
        fallback = f"Yo {speaker}, I'm having some connection issues but I'm still vibing with you. What's the move?"
    else:
        fallback = f"What's good, {speaker}? I'm dealing with some technical stuff right now, but your Agent Lee is still here. Let's work through this."
    return jsonify({
        "response": fallback,
        "mode": "fallback",
        "error": error,
        "speaker": speaker,
        "tool": "llm_com"
    })

def run_tool(input_text, speaker="unknown"):
    """
    Enhanced legacy compatibility function with IoT awareness
    Converts old trigger-based system to new dispatch with IoT routing
    """
    # Check for IoT-related commands first
    if is_iot_related_task(input_text):
        print(f"[IoT] Detected IoT command: {input_text}")
        return dispatch(input_text, speaker)  # Let dispatch handle IoT routing

    # Legacy trigger-based routing
    for tool_name, tool_info in TOOL_REGISTRY.items():
        if 'trigger' in tool_info and tool_info['trigger'] in input_text.lower():
            task = {"tool": tool_name, "input": input_text}
            return dispatch(task, speaker)

    # Default to LLM chat
    task = {"tool": "llm_chat", "input": input_text}
    return dispatch(task, speaker)

def get_iot_status():
    """Get current IoT system status for monitoring."""
    try:
        device_states = get_iot_device_states()
        return {
            "status": "active",
            "device_count": len(device_states),
            "online_devices": len([d for d in device_states.values() if d.get('online', False)]),
            "last_update": IOT_CACHE_TIMESTAMP.isoformat() if IOT_CACHE_TIMESTAMP else None,
            "controller_url": IOT_CONTROLLER_URL,
            "home_assistant_url": HOME_ASSISTANT_URL
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "device_count": 0,
            "online_devices": 0
        }

# Legacy CLI compatibility
if __name__ == "__main__":
    import sys
    text = sys.argv[1] if len(sys.argv) > 1 else ""
    result = dispatch({"tool": "llm_chat", "input": text}, "cli_user")
    if FLASK_AVAILABLE:
        print(result.get_json().get("response", "Communication failed"))
    else:
        print(result.get("response", "Communication failed"))
