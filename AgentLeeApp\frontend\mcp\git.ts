import { MCPServer } from 'model-context-protocol';
import simpleGit from 'simple-git';

export async function startServer(port = 3014, repoPath: string) {
    const git = simpleGit(repoPath);
    const server = new MCPServer({ port });
    server.registerTool('gitStatus', {
        title: 'Git Status',
        parameters: { type: 'object', properties: {} },
        returns: { type: 'object', properties: { status: { type: 'string' }, branch: { type: 'string' } } }
    }, async () => {
        const status = await git.status();
        return { status: status.summary, branch: status.current };
    });
    server.registerTool('gitList', {
        title: 'Git File List',
        parameters: { type: 'object', properties: {} },
        returns: { type: 'object', properties: { files: { type: 'array', items: { type: 'string' } } } }
    }, async () => ({ files: (await git.raw(['ls-files'])).split('\n').filter(<PERSON><PERSON><PERSON>) }));
    await server.listen();
}