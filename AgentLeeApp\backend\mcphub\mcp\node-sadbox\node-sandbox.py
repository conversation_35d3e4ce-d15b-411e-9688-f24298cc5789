import subprocess
import uuid
import os

class NodeSandboxMCP:
    def __init__(self, scripts_dir="node_scripts"):
        os.makedirs(scripts_dir, exist_ok=True)
        self.scripts_dir = scripts_dir

    def run_js(self, code: str):
        file_id = str(uuid.uuid4())
        file_path = os.path.join(self.scripts_dir, f"{file_id}.js")
        
        with open(file_path, "w") as f:
            f.write(code)

        try:
            result = subprocess.run(["node", file_path], capture_output=True, text=True, timeout=10)
            return {
                "stdout": result.stdout.strip(),
                "stderr": result.stderr.strip(),
                "exit_code": result.returncode
            }
        except subprocess.TimeoutExpired:
            return {"error": "Script timed out."}
        except Exception as e:
            return {"error": str(e)}
        finally:
            os.remove(file_path)
