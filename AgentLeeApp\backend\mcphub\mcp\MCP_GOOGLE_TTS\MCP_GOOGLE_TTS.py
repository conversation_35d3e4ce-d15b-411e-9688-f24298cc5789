from google.cloud import texttospeech

class GoogleTTSMCP:
    def __init__(self, credentials_path="your-service-account.json"):
        self.client = texttospeech.TextToSpeechClient.from_service_account_file(credentials_path)

    def synthesize(self, text: str, voice_name: str = "en-US-Wavenet-D", language_code: str = "en-US", output_path="output.mp3"):
        synthesis_input = texttospeech.SynthesisInput(text=text)
        voice = texttospeech.VoiceSelectionParams(language_code=language_code, name=voice_name)
        audio_config = texttospeech.AudioConfig(audio_encoding=texttospeech.AudioEncoding.MP3)

        response = self.client.synthesize_speech(input=synthesis_input, voice=voice, audio_config=audio_config)

        with open(output_path, "wb") as out:
            out.write(response.audio_content)

        return {"status": "success", "file": output_path}
