from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Body
from MCP_HOME_ASSISTANT import HomeAssistantMCP

# Replace with your actual Home Assistant URL and token
mcp = HomeAssistantMCP(base_url="http://localhost:8123", token="YOUR_LONG_LIVED_TOKEN")

app = FastAPI()

@app.get("/homeassistant/entities")
def get_entities():
    try:
        return mcp.list_entities()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/homeassistant/service")
def trigger_service(domain: str, service: str, payload: dict = Body(...)):
    try:
        return mcp.call_service(domain, service, payload)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
