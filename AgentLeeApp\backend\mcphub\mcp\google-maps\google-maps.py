import requests

class GoogleMapsMCP:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base = "https://maps.googleapis.com/maps/api"

    def geocode_address(self, address):
        url = f"{self.base}/geocode/json"
        params = {"address": address, "key": self.api_key}
        res = requests.get(url, params=params)
        res.raise_for_status()
        return res.json()

    def search_nearby(self, lat, lng, radius, keyword=""):
        url = f"{self.base}/place/nearbysearch/json"
        params = {
            "location": f"{lat},{lng}",
            "radius": radius,
            "keyword": keyword,
            "key": self.api_key
        }
        res = requests.get(url, params=params)
        res.raise_for_status()
        return res.json()
