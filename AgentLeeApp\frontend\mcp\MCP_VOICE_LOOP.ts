
/**
 * Voice Loop MCP Server
 * Provides audio processing and looping capabilities
 */

import { MC<PERSON>erver, MCPTool, createToolSchema, ToolPropertyTypes } from '../mcp-base';

class VoiceLoopMCPServer extends MCPServer {
  constructor() {
    super({
      name: 'Voice Loop MCP Server',
      version: '1.0.0',
      port: 3005
    });

    this.setupTools();
  }

  private setupTools(): void {
    // Voice Loop Tool
    const voiceLoopTool: MCPTool = {
      name: 'voiceLoop',
      description: 'Echo back audio repeatedly for testing',
      inputSchema: createToolSchema({
        audio_base64: ToolPropertyTypes.string('Base64 encoded audio data'),
        loops: ToolPropertyTypes.number('Number of times to loop the audio', 1, 10),
        format: ToolPropertyTypes.string('Audio format (wav, mp3, etc.)', 'wav'),
        addSilence: ToolPropertyTypes.boolean('Add silence between loops', false),
        silenceDuration: ToolPropertyTypes.number('Silence duration in milliseconds', 0, 5000)
      }, ['audio_base64']),
      handler: async ({ audio_base64, loops = 1, format = 'wav', addSilence = false, silenceDuration = 500 }: {
        audio_base64: string;
        loops?: number;
        format?: string;
        addSilence?: boolean;
        silenceDuration?: number;
      }) => {
        try {
          if (!audio_base64) {
            return {
              success: false,
              error: 'Audio data is required'
            };
          }

          // Validate base64
          try {
            Buffer.from(audio_base64, 'base64');
          } catch (error) {
            return {
              success: false,
              error: 'Invalid base64 audio data'
            };
          }

          let result_audio_base64 = audio_base64;

          // Create silence buffer if needed (simplified - just empty data)
          const silenceBuffer = addSilence ? Buffer.alloc(Math.floor(silenceDuration / 10)).toString('base64') : '';

          // Loop the audio
          for (let i = 1; i < loops; i++) {
            if (addSilence && silenceBuffer) {
              result_audio_base64 += silenceBuffer;
            }
            result_audio_base64 += audio_base64;
          }

          return {
            success: true,
            result_audio_base64,
            metadata: {
              originalSize: audio_base64.length,
              resultSize: result_audio_base64.length,
              loops,
              format,
              addedSilence: addSilence,
              silenceDuration: addSilence ? silenceDuration : 0
            }
          };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          return {
            success: false,
            error: errorMessage
          };
        }
      }
    };

    // Audio Info Tool
    const audioInfoTool: MCPTool = {
      name: 'audioInfo',
      description: 'Get information about base64 encoded audio',
      inputSchema: createToolSchema({
        audio_base64: ToolPropertyTypes.string('Base64 encoded audio data')
      }, ['audio_base64']),
      handler: async ({ audio_base64 }: { audio_base64: string }) => {
        try {
          if (!audio_base64) {
            return {
              success: false,
              error: 'Audio data is required'
            };
          }

          // Validate base64
          let buffer: Buffer;
          try {
            buffer = Buffer.from(audio_base64, 'base64');
          } catch (error) {
            return {
              success: false,
              error: 'Invalid base64 audio data'
            };
          }

          return {
            success: true,
            info: {
              base64Length: audio_base64.length,
              bufferSize: buffer.length,
              sizeKB: (buffer.length / 1024).toFixed(2),
              estimatedDurationSeconds: (buffer.length / 44100 / 2).toFixed(2) // Rough estimate for 16-bit 44.1kHz
            }
          };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          return {
            success: false,
            error: errorMessage
          };
        }
      }
    };

    // Audio Reverse Tool
    const audioReverseTool: MCPTool = {
      name: 'audioReverse',
      description: 'Reverse base64 encoded audio data',
      inputSchema: createToolSchema({
        audio_base64: ToolPropertyTypes.string('Base64 encoded audio data')
      }, ['audio_base64']),
      handler: async ({ audio_base64 }: { audio_base64: string }) => {
        try {
          if (!audio_base64) {
            return {
              success: false,
              error: 'Audio data is required'
            };
          }

          // Validate base64
          let buffer: Buffer;
          try {
            buffer = Buffer.from(audio_base64, 'base64');
          } catch (error) {
            return {
              success: false,
              error: 'Invalid base64 audio data'
            };
          }

          // Simple reverse (byte-level, not proper audio reverse)
          const reversedBuffer = Buffer.from(buffer).reverse();
          const reversed_audio_base64 = reversedBuffer.toString('base64');

          return {
            success: true,
            reversed_audio_base64,
            metadata: {
              originalSize: buffer.length,
              reversedSize: reversedBuffer.length
            }
          };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          return {
            success: false,
            error: errorMessage
          };
        }
      }
    };

    // Add all tools
    this.addTool(voiceLoopTool);
    this.addTool(audioInfoTool);
    this.addTool(audioReverseTool);
  }

  protected override getMetrics(): any {
    const baseMetrics = super.getMetrics();
    return {
      ...baseMetrics,
      supportedFormats: ['wav', 'mp3', 'aac', 'ogg'],
      maxLoops: 10,
      maxSilenceDuration: 5000
    };
  }
}

// Export for use in other modules
export async function startServer(port = 3005): Promise<VoiceLoopMCPServer> {
  const server = new VoiceLoopMCPServer();
  await server.start(port);
  return server;
}

// Start server if this file is run directly
if (require.main === module) {
  startServer().catch(console.error);
}
