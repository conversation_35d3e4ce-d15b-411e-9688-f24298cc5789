from fastapi import FastAP<PERSON>, HTTPException
from fastapi.responses import StreamingResponse
from MCP_VIDEO_STREAMER import VideoStreamerMCP
import io
import time

mcp = VideoStreamerMCP()
app = FastAPI()

@app.get("/video/start")
def start_video():
    try:
        return mcp.start_stream()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/video/frame")
def get_video_frame():
    try:
        frame = mcp.read_frame()
        if isinstance(frame, dict) and "error" in frame:
            raise HTTPException(status_code=400, detail=frame["error"])
        return StreamingResponse(io.BytesIO(frame), media_type="image/jpeg")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/video/stop")
def stop_video():
    try:
        return mcp.stop_stream()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
