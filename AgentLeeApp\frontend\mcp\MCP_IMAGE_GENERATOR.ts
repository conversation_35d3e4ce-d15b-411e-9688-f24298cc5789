import { MCPServer } from 'model-context-protocol';
import axios from 'axios';

export async function startServer(port = 3031, aiEndpoint: string) {
    const server = new MCPServer({ port });
    server.registerTool('generateImage', {
        title: 'AI Image Generator',
        parameters: {
            type: 'object',
            properties: { prompt: { type: 'string' }, count: { type: 'integer', default: 1 } },
            required: ['prompt']
        },
        returns: { type: 'object', properties: { images_base64: { type: 'array', items: { type: 'string' } } } }
    }, async ({ prompt, count }) => {
        const resp = await axios.post(aiEndpoint, { prompt, count });
        return { images_base64: resp.data.images };
    });
    await server.listen();
}