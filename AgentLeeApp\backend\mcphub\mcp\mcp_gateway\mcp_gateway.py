import requests

class MCPGateway:
    def __init__(self):
        self.routes = {}

    def register_route(self, name: str, url: str):
        self.routes[name] = url
        return {"status": "registered", "route": name, "url": url}

    def forward_request(self, route: str, payload: dict):
        if route not in self.routes:
            return {"error": f"Route '{route}' not registered."}
        try:
            response = requests.post(self.routes[route], json=payload)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            return {"error": str(e)}

    def list_routes(self):
        return self.routes
