from fastapi import FastAPI, HTTPException, Query, Body
from MCP_CONSOLE_LOGGER import ConsoleLoggerMCP

app = FastAPI()
mcp = ConsoleLoggerMCP()

@app.post("/logger/log")
def log_message(message: str = Body(...), level: str = Body("info")):
    try:
        return mcp.log_message(message, level)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/logger/latest")
def get_logs(lines: int = Query(10)):
    try:
        return mcp.get_latest_logs(lines)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
