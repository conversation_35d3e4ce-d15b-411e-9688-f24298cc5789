from fastapi import FastAP<PERSON>, Query, HTTPException
from health_monitor import HealthMonitorMCP

app = FastAPI()
mcp = HealthMonitorMCP()

@app.get("/health")
def get_health():
    try:
        return mcp.get_system_health()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/processes")
def get_top_processes(limit: int = Query(10, ge=1, le=100)):
    try:
        return mcp.list_processes(limit)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
