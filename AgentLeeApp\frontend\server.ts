import express from 'express';
import cors from 'cors';
import { execFile, ExecFileException } from 'child_process';
import path from 'path';

const app = express();
const PORT = process.env.PORT || 8000;

app.use(cors());
app.use(express.json());

// POST /run-mcp endpoint
app.post('/run-mcp', async (req, res) => {
  const { args, input } = req.body;
  if (!Array.isArray(args) || !args.length) {
    return res.status(400).send('No MCP tool specified.');
  }

  // For demo: run a local MCP script (simulate Docker)
  const toolName = args[args.length - 1];
  const toolPath = path.join(__dirname, 'mcp', toolName + '.ts');

  try {
    // In production, you would use Docker or a real process
    // Here, we just simulate by running a local script
    execFile('ts-node', [toolPath, ...(input ? [input] : [])], (error: ExecFileException | null, stdout: string, stderr: string) => {
      if (error) {
        return res.status(500).send(stderr || error.message);
      }
      res.send(stdout);
    });
  } catch (err: any) {
    res.status(500).send(err.message);
  }
});

app.listen(PORT, () => {
  console.log(`MCP backend server running on port ${PORT}`);
});
