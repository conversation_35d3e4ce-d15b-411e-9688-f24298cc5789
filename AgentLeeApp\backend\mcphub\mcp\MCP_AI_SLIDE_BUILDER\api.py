from fastapi import FastAP<PERSON>, HTTPException, Body
from MCP_AI_SLIDE_BUILDER import AISlideBuilderMCP

app = FastAPI()
mcp = AISlideBuilderMCP()

@app.post("/slides/create")
def create_slide(title: str = Body(...), bullets: list = Body(...), filename: str = "presentation.pptx"):
    try:
        return mcp.create_slide_deck(title, bullets, filename)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
