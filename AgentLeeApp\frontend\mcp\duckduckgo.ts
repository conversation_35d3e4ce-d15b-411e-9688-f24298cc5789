import { MCPServer } from 'model-context-protocol';
import axios from 'axios';

export async function startServer(port = 3013) {
    const server = new MCPServer({ port });
    server.registerTool('ddgSearch', {
        title: 'DuckDuckGo Search',
        parameters: { type: 'object', properties: { query: { type: 'string' } }, required: ['query'] },
        returns: { type: 'object', properties: { results: { type: 'array', items: { type: 'object', properties: { title: { type: 'string' }, url: { type: 'string' } } } } }
    }, async ({ query }) => {
        const resp = await axios.get(`https://api.duckduckgo.com/?q=${encodeURIComponent(query)}&format=json`);
        const topics = resp.data.RelatedTopics || [];
        const results = topics.slice(0, 5).map((t: any) => ({
            title: t.Text,
            url: t.FirstURL
        }));
        return { results };
    });
    await server.listen();
}