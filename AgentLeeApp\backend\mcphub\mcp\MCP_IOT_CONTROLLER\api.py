from fastapi import FastAPI, HTTPException, Body
from MCP_IOT_CONTROLLER import IoTControllerMCP

mcp = IoTControllerMCP()
app = FastAPI()

@app.get("/iot/connect")
def connect_to_broker():
    try:
        return mcp.connect()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/iot/publish")
def publish_message(topic: str = Body(...), payload: str = Body(...)):
    try:
        return mcp.publish(topic, payload)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/iot/disconnect")
def disconnect_broker():
    try:
        return mcp.disconnect()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
