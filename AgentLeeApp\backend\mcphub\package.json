{"name": "mcphub", "version": "1.0.0", "description": "MCP Hub for <PERSON> Lee - Complete MCP ecosystem", "main": "main.ts", "scripts": {"start": "node dist/main.js", "dev": "ts-node main.ts", "dev:gateway": "ts-node mcp-gateway.ts", "dev:monitor": "ts-node health-monitor.ts", "dev:webhooks": "ts-node webhook-system.ts", "build": "tsc", "build:gateway": "tsc mcp-gateway.ts --outDir dist/gateway", "build:monitor": "tsc health-monitor.ts --outDir dist/monitor", "build:webhooks": "tsc webhook-system.ts --outDir dist/webhooks", "test": "jest", "test:unit": "jest --testPathPattern=tests/unit", "test:integration": "jest --testPathPattern=tests/integration", "test:integration:gateway": "jest --testPathPattern=tests/integration/gateway", "test:integration:hub": "jest --testPathPattern=tests/integration/hub", "test:integration:monitor": "jest --testPathPattern=tests/integration/monitor", "test:integration:webhooks": "jest --testPathPattern=tests/integration/webhooks", "test:e2e": "jest --testPathPattern=tests/e2e", "lint": "eslint . --ext .ts,.js", "lint:fix": "eslint . --ext .ts,.js --fix", "type-check": "tsc --noEmit", "clean": "rm -rf dist", "docker:build": "docker build -t mcphub .", "docker:build:gateway": "docker build -f Dockerfile.gateway -t mcphub-gateway .", "docker:build:monitor": "docker build -f Dockerfile.monitor -t mcphub-monitor .", "docker:build:webhooks": "docker build -f Dockerfile.webhooks -t mcphub-webhooks ."}, "keywords": ["mcp", "model-context-protocol", "ai", "agent", "microservices", "typescript", "nodejs"], "author": "Agent <PERSON>", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"@notionhq/client": "^2.2.0", "axios": "^1.5.0", "express": "^4.18.2", "cors": "^2.8.5", "http-proxy-middleware": "^2.0.6", "express-rate-limit": "^7.1.5", "crypto": "^1.0.1", "redis": "^4.6.10", "pg": "^8.11.3", "model-context-protocol": "^1.0.0", "node-telegram-bot-api": "^0.64.0", "puppeteer": "^21.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/pg": "^8.10.7", "@types/node-telegram-bot-api": "^0.64.0", "typescript": "^5.0.0", "ts-node": "^10.9.0", "jest": "^29.0.0", "@types/jest": "^29.5.8", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "supertest": "^6.3.3", "@types/supertest": "^2.0.16"}}