import { MCPServer } from 'model-context-protocol';
import fs from 'fs';
import { spawnSync } from 'child_process';

export async function startServer(port = 3019) {
    const server = new MCPServer({ port });
    server.registerTool('checkSilence', {
        title: 'Audio Leak Monitor',
        parameters: {
            type: 'object',
            properties: {
                filePath: { type: 'string' }
            },
            required: ['filePath']
        },
        returns: {
            type: 'object',
            properties: {
                isSilent: { type: 'boolean' },
                dbLevel: { type: 'number' }
            }
        }
    }, async ({ filePath }) => {
        const stats = spawnSync('ffmpeg', ['-i', filePath, '-af', 'volumedetect', '-f', 'null', 'NUL'], { encoding: 'utf-8' });
        const match = stats.stderr.match(/mean_volume:\s*(-?\d+(\.\d+)?) dB/);
        const level = match ? parseFloat(match[1]) : NaN;
        return { isSilent: level < -50, dbLevel: level };
    });
    await server.listen();
}