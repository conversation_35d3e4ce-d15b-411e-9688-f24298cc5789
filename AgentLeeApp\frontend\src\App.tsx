import React, { useCallback, useState } from 'react';

// Core components for the UI layout
import { Header } from './components/Header';
import { SearchBar } from './components/SearchBar';
import { OutputCard } from './components/OutputCard';
import { LiveKitController } from './components/LiveKitController';

// The new, centralized hook for all agent logic and state
import { useAgentLee } from './hooks/useAgentLee';

const App: React.FC = () => {
  // All core logic (messages, speaking state, sending messages) now comes from our custom hook.
  const { messages, sendMessage } = useAgentLee();

  // State that is specific to the UI, like loading indicators.
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Handles the search action from the SearchBar component.
   */
  const handleSearch = useCallback(async (query: string) => {
    setIsLoading(true);
    setError(null);
    try {
      await sendMessage(query);
    } catch (e: any) {
      setError(e.message || "An unexpected error occurred.");
    }
    setIsLoading(false);
  }, [sendMessage]);

  /**
   * A callback for handling messages that might come directly from the LiveKit room.
   */
  const handleLiveKitMessage = useCallback((message: string) => {
    console.log("Message received from LiveKit:", message);
    // Future enhancement: Add this message to the chat log via the useAgentLee hook.
  }, []);

  // Derive the latest agent response from the messages array.
  const latestAgentResponse = [...messages].reverse().find(m => m.sender === 'agent')?.text 
    || "Welcome! I'm Agent Lee. How can I help you today?";

  return (
    <div className="min-h-screen bg-gray-900 font-sans text-white">
      <div className="container mx-auto px-6 py-4">
        <Header avatar="./assets/agentlee_avatar.png" />
      </div>

      <main className="container mx-auto px-6 py-6 space-y-6">
        <SearchBar onSearch={handleSearch} isLoading={isLoading} />

        {/* Main 3-column grid layout for the dashboard */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          
          {/* Left Column (takes 2/3 of the space on large screens) */}
          <div className="xl:col-span-2">
            <OutputCard title="🎥 Live Connection & Voice Control" className="h-full min-h-[600px]">
              <LiveKitController onMessage={handleLiveKitMessage} />
            </OutputCard>
          </div>

          {/* Right Column (takes 1/3 of the space on large screens) */}
          <div className="space-y-6">
            <OutputCard title="💬 Agent Response" className="min-h-[250px]">
              {isLoading ? (
                <div className="flex items-center justify-center h-full">
                  <div className="flex items-center space-x-3">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                    <span className="text-gray-400">Agent Lee is thinking...</span>
                  </div>
                </div>
              ) : (
                <p className="text-lg leading-relaxed">
                  {error || latestAgentResponse}
                </p>
              )}
            </OutputCard>

            <OutputCard title="💭 Conversation History">
              <div className="max-h-[400px] overflow-y-auto space-y-3 pr-2">
                {messages.map((message) => (
                  <div key={message.id} className="flex items-start space-x-3 text-sm">
                    <span className={`flex-shrink-0 font-medium ${
                      message.sender === 'user' ? 'text-blue-400' : 'text-green-400'
                    }`}>
                      {message.sender === 'user' ? '👤 You:' : '🤖 Lee:'}
                    </span>
                    <p className="text-gray-300 flex-1">{message.text}</p>
                  </div>
                ))}
              </div>
            </OutputCard>
          </div>
        </div>
      </main>
    </div>
  );
};

export default App;
