class MCPBase:
    def __init__(self, name: str):
        self.name = name
        self.status = "initialized"

    def ping(self):
        return {"mcp": self.name, "status": self.status}

    def identify(self):
        return {
            "name": self.name,
            "type": "base",
            "version": "1.0",
            "status": self.status
        }

    def reset(self):
        self.status = "reset"
        return {"status": self.status}
