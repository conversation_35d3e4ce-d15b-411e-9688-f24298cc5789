name: MCP CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'backend/mcphub/**'
      - 'frontend/**'
      - 'docker-compose.mcp.yml'
      - '.github/workflows/mcp-ci-cd.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'backend/mcphub/**'
      - 'frontend/**'
      - 'docker-compose.mcp.yml'

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}/mcp

jobs:
  # Test and lint MCP services
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18, 20]
        service: [gateway, hub, monitor, webhooks]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: backend/mcphub/package-lock.json

    - name: Install dependencies
      run: |
        cd backend/mcphub
        npm ci

    - name: Run linting
      run: |
        cd backend/mcphub
        npm run lint

    - name: Run type checking
      run: |
        cd backend/mcphub
        npm run type-check

    - name: Run unit tests
      run: |
        cd backend/mcphub
        npm run test:unit
      env:
        NODE_ENV: test

    - name: Run integration tests for ${{ matrix.service }}
      run: |
        cd backend/mcphub
        npm run test:integration:${{ matrix.service }}
      env:
        NODE_ENV: test

  # Security scanning
  security:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: './backend/mcphub'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

    - name: Run npm audit
      run: |
        cd backend/mcphub
        npm audit --audit-level moderate

  # Build and test Docker images
  build:
    runs-on: ubuntu-latest
    needs: [test, security]
    strategy:
      matrix:
        service: [gateway, hub, monitor, webhooks]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-${{ matrix.service }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./backend/mcphub
        file: ./backend/mcphub/Dockerfile.${{ matrix.service }}
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64

  # End-to-end testing
  e2e-test:
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Create test environment
      run: |
        cp docker-compose.mcp.yml docker-compose.test.yml
        # Override with test images
        sed -i 's|build:|image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-gateway:${{ github.sha }}|g' docker-compose.test.yml

    - name: Start MCP services
      run: |
        docker-compose -f docker-compose.test.yml up -d
        sleep 30  # Wait for services to start

    - name: Wait for services to be healthy
      run: |
        timeout 300 bash -c 'until curl -f http://localhost:3000/health; do sleep 5; done'
        timeout 300 bash -c 'until curl -f http://localhost:3001/health; do sleep 5; done'

    - name: Run E2E tests
      run: |
        cd backend/mcphub
        npm run test:e2e
      env:
        MCP_GATEWAY_URL: http://localhost:3000
        MCP_WEBHOOK_URL: http://localhost:3001

    - name: Collect logs on failure
      if: failure()
      run: |
        docker-compose -f docker-compose.test.yml logs > test-logs.txt
        cat test-logs.txt

    - name: Cleanup test environment
      if: always()
      run: |
        docker-compose -f docker-compose.test.yml down -v

  # Deploy to staging
  deploy-staging:
    runs-on: ubuntu-latest
    needs: [build, e2e-test]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Add your staging deployment commands here
        # Example: kubectl apply -f k8s/staging/
      env:
        STAGING_URL: ${{ secrets.STAGING_URL }}
        STAGING_TOKEN: ${{ secrets.STAGING_TOKEN }}

    - name: Run smoke tests
      run: |
        timeout 300 bash -c 'until curl -f ${{ secrets.STAGING_URL }}/health; do sleep 10; done'
        curl -f ${{ secrets.STAGING_URL }}/api/services

  # Deploy to production
  deploy-production:
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to Fly.io
      uses: superfly/flyctl-actions/setup-flyctl@master
    
    - name: Deploy MCP Gateway
      run: |
        cd backend/mcphub
        flyctl deploy --config fly.gateway.toml --image ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-gateway:${{ github.sha }}
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Deploy MCP Hub
      run: |
        cd backend/mcphub
        flyctl deploy --config fly.hub.toml --image ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-hub:${{ github.sha }}
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Deploy Webhook System
      run: |
        cd backend/mcphub
        flyctl deploy --config fly.webhooks.toml --image ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-webhooks:${{ github.sha }}
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Run production smoke tests
      run: |
        timeout 300 bash -c 'until curl -f https://agentlee.fly.dev/health; do sleep 10; done'
        curl -f https://agentlee.fly.dev/api/services

    - name: Notify deployment success
      if: success()
      run: |
        curl -X POST ${{ secrets.DISCORD_WEBHOOK_URL }} \
          -H "Content-Type: application/json" \
          -d '{
            "content": "🚀 MCP services deployed successfully to production!",
            "embeds": [{
              "title": "Deployment Success",
              "description": "All MCP services are now live",
              "color": 3066993,
              "fields": [
                {"name": "Commit", "value": "${{ github.sha }}", "inline": true},
                {"name": "Branch", "value": "${{ github.ref_name }}", "inline": true},
                {"name": "URL", "value": "https://agentlee.fly.dev", "inline": true}
              ]
            }]
          }'

    - name: Notify deployment failure
      if: failure()
      run: |
        curl -X POST ${{ secrets.DISCORD_WEBHOOK_URL }} \
          -H "Content-Type: application/json" \
          -d '{
            "content": "❌ MCP deployment failed!",
            "embeds": [{
              "title": "Deployment Failed",
              "description": "Check the logs for details",
              "color": 15158332,
              "fields": [
                {"name": "Commit", "value": "${{ github.sha }}", "inline": true},
                {"name": "Branch", "value": "${{ github.ref_name }}", "inline": true},
                {"name": "Logs", "value": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}", "inline": true}
              ]
            }]
          }'

  # Performance testing
  performance-test:
    runs-on: ubuntu-latest
    needs: deploy-production
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'

    - name: Install k6
      run: |
        sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
        echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
        sudo apt-get update
        sudo apt-get install k6

    - name: Run performance tests
      run: |
        cd backend/mcphub/tests/performance
        k6 run --out json=results.json load-test.js
      env:
        TARGET_URL: https://agentlee.fly.dev

    - name: Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: backend/mcphub/tests/performance/results.json

  # Cleanup old images
  cleanup:
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Delete old container images
      uses: actions/delete-package-versions@v4
      with:
        package-name: ${{ env.IMAGE_NAME }}-gateway
        package-type: container
        min-versions-to-keep: 5
        delete-only-untagged-versions: true
