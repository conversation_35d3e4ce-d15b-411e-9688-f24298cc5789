from fastapi import FastAP<PERSON>, HTTPException, Body
from desktop_commander import DesktopCommanderMCP

app = FastAPI()
mcp = DesktopCommanderMCP()

@app.post("/desktop/run")
def run_shell_command(cmd: str = Body(...)):
    try:
        return mcp.run_command(cmd)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/desktop/open")
def open_path(path: str = Body(...)):
    try:
        return mcp.open_file_or_url(path)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
