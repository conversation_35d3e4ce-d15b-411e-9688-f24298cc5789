import { MCPServer } from '../mcp-base';
import { MCPTool, MCPResource, ToolPropertyTypes, createToolSchema } from '../mcp-base';
import { Request, Response } from 'express';
import axios from 'axios';

interface AgentLeeConfig {
  baseUrl: string;
  apiKey?: string;
  timeout: number;
}

interface TaskResult {
  success: boolean;
  response?: any;
  error?: string;
  speaker?: string;
  timestamp: Date;
}

interface MemoryEntry {
  speaker: string;
  content: string;
  timestamp: string;
}

interface Session {
  speaker: string;
  session_id: string;
  start_time: string;
  last_activity: string;
  messages: number;
}

class AgentLeeBridgeMCPServer extends MCPServer {
  private agentLeeConfig: AgentLeeConfig;
  private sessionCache: Map<string, Session> = new Map();

  constructor(port: number = 3013) {
    super({
      name: 'Agent Lee Bridge MCP Server',
      version: '1.0.0',
      port,
      enableMetrics: true,
      enableHealthCheck: true
    });

    // Load Agent Lee backend configuration
    this.agentLeeConfig = {
      baseUrl: process.env.AGENT_LEE_URL || 'http://localhost:5000',
      apiKey: process.env.AGENT_LEE_API_KEY,
      timeout: parseInt(process.env.AGENT_LEE_TIMEOUT || '30000')
    };

    this.initializeTools();
    this.initializeResources();
  }

  private initializeTools() {
    // Task Parsing Tool
    const parseTaskTool: MCPTool = {
      name: 'parseTask',
      description: 'Parse user input and infer the appropriate task or tool using Agent Lee',
      inputSchema: createToolSchema({
        speaker: ToolPropertyTypes.string('Speaker/user identifier'),
        text: ToolPropertyTypes.string('User input text to parse'),
        tool: ToolPropertyTypes.string('Specific tool to use (optional)', false)
      }),
      handler: async (params: any) => {
        return await this.parseTask(params.speaker, params.text, params.tool);
      }
    };

    // Memory Search Tool
    const searchMemoryTool: MCPTool = {
      name: 'searchMemory',
      description: 'Search Agent Lee memory for previous conversations and interactions',
      inputSchema: createToolSchema({
        speaker: ToolPropertyTypes.string('Speaker to search memory for', false),
        query: ToolPropertyTypes.string('Search query', false),
        limit: ToolPropertyTypes.number('Maximum number of results', false)
      }),
      handler: async (params: any) => {
        return await this.searchMemory(params.speaker, params.query, params.limit);
      }
    };

    // Voice Command Tool
    const processVoiceCommandTool: MCPTool = {
      name: 'processVoiceCommand',
      description: 'Process voice commands through Agent Lee with IoT integration',
      inputSchema: createToolSchema({
        speaker: ToolPropertyTypes.string('Speaker identifier'),
        command: ToolPropertyTypes.string('Voice command text'),
        context: ToolPropertyTypes.object('Additional context (location, devices, etc.)', false)
      }),
      handler: async (params: any) => {
        return await this.processVoiceCommand(params.speaker, params.command, params.context);
      }
    };

    // Get Speaker Stats Tool
    const getSpeakerStatsTool: MCPTool = {
      name: 'getSpeakerStats',
      description: 'Get statistics and information about a specific speaker',
      inputSchema: createToolSchema({
        speaker: ToolPropertyTypes.string('Speaker identifier')
      }),
      handler: async (params: any) => {
        return await this.getSpeakerStats(params.speaker);
      }
    };

    // List Tools Tool
    const listToolsTool: MCPTool = {
      name: 'listAgentLeeTools',
      description: 'List all available tools in the Agent Lee backend',
      inputSchema: createToolSchema({}),
      handler: async (params: any) => {
        return await this.listAgentLeeTools();
      }
    };

    // Generate Badge Tool
    const generateBadgeTool: MCPTool = {
      name: 'generateBadge',
      description: 'Generate a badge for a speaker based on their activities',
      inputSchema: createToolSchema({
        speaker: ToolPropertyTypes.string('Speaker identifier'),
        badge_type: ToolPropertyTypes.string('Type of badge to generate', false)
      }),
      handler: async (params: any) => {
        return await this.generateBadge(params.speaker, params.badge_type);
      }
    };

    // Generate Certificate Tool
    const generateCertificateTool: MCPTool = {
      name: 'generateCertificate',
      description: 'Generate a certificate for a speaker',
      inputSchema: createToolSchema({
        speaker: ToolPropertyTypes.string('Speaker identifier'),
        certificate_type: ToolPropertyTypes.string('Type of certificate', false),
        achievements: ToolPropertyTypes.array('List of achievements', false)
      }),
      handler: async (params: any) => {
        return await this.generateCertificate(params.speaker, params.certificate_type, params.achievements);
      }
    };

    // Health Check Tool
    const healthCheckTool: MCPTool = {
      name: 'healthCheck',
      description: 'Check the health status of Agent Lee backend services',
      inputSchema: createToolSchema({}),
      handler: async (params: any) => {
        return await this.healthCheck();
      }
    };

    // Register all tools
    this.addTool(parseTaskTool);
    this.addTool(searchMemoryTool);
    this.addTool(processVoiceCommandTool);
    this.addTool(getSpeakerStatsTool);
    this.addTool(listToolsTool);
    this.addTool(generateBadgeTool);
    this.addTool(generateCertificateTool);
    this.addTool(healthCheckTool);
  }

  private initializeResources() {
    // Sessions Resource
    const sessionsResource: MCPResource = {
      uri: 'agentlee://sessions',
      name: 'Active Sessions',
      description: 'List of active user sessions in Agent Lee',
      mimeType: 'application/json'
    };

    // Memory Resource
    const memoryResource: MCPResource = {
      uri: 'agentlee://memory',
      name: 'Memory Database',
      description: 'Agent Lee memory database with conversation history',
      mimeType: 'application/json'
    };

    // Leaderboard Resource
    const leaderboardResource: MCPResource = {
      uri: 'agentlee://leaderboard',
      name: 'Badge Leaderboard',
      description: 'Badge leaderboard showing top performers',
      mimeType: 'application/json'
    };

    // Tools Resource
    const toolsResource: MCPResource = {
      uri: 'agentlee://tools',
      name: 'Available Tools',
      description: 'List of all available tools in Agent Lee backend',
      mimeType: 'application/json'
    };

    // System Status Resource
    const systemStatusResource: MCPResource = {
      uri: 'agentlee://system-status',
      name: 'System Status',
      description: 'Current system status and health metrics',
      mimeType: 'application/json'
    };

    this.addResource(sessionsResource);
    this.addResource(memoryResource);
    this.addResource(leaderboardResource);
    this.addResource(toolsResource);
    this.addResource(systemStatusResource);
  }

  private async makeAgentLeeRequest(endpoint: string, method: 'GET' | 'POST' = 'GET', data?: any): Promise<any> {
    try {
      const config: any = {
        method,
        url: `${this.agentLeeConfig.baseUrl}${endpoint}`,
        timeout: this.agentLeeConfig.timeout,
        headers: {
          'Content-Type': 'application/json'
        }
      };

      if (this.agentLeeConfig.apiKey) {
        config.headers['Authorization'] = `Bearer ${this.agentLeeConfig.apiKey}`;
      }

      if (data && method === 'POST') {
        config.data = data;
      }

      const response = await axios(config);
      return {
        success: true,
        data: response.data,
        status: response.status
      };

    } catch (error) {
      if (axios.isAxiosError(error)) {
        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          data: error.response?.data
        };
      }
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async parseTask(speaker: string, text: string, tool?: string): Promise<TaskResult> {
    try {
      const payload: any = { speaker, text };
      if (tool) payload.tool = tool;

      const result = await this.makeAgentLeeRequest('/api/task/parse', 'POST', payload);
      
      return {
        success: result.success,
        response: result.data,
        error: result.success ? undefined : result.error,
        speaker,
        timestamp: new Date()
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Task parsing failed',
        speaker,
        timestamp: new Date()
      };
    }
  }

  private async searchMemory(speaker?: string, query?: string, limit?: number): Promise<any> {
    try {
      let endpoint = '/api/memory/search?';
      const params = new URLSearchParams();
      
      if (speaker) params.append('speaker', speaker);
      if (query) params.append('query', query);
      if (limit) params.append('limit', limit.toString());

      const result = await this.makeAgentLeeRequest(`/api/memory/search?${params.toString()}`);
      
      return {
        success: result.success,
        results: result.data?.results || [],
        total: result.data?.results?.length || 0,
        error: result.success ? undefined : result.error
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Memory search failed'
      };
    }
  }

  private async processVoiceCommand(speaker: string, command: string, context?: any): Promise<any> {
    try {
      // First, parse the command through Agent Lee
      const parseResult = await this.parseTask(speaker, command);
      
      if (!parseResult.success) {
        return parseResult;
      }

      // Check if this is an IoT-related command
      const iotKeywords = ['light', 'lights', 'camera', 'lock', 'unlock', 'turn on', 'turn off', 'dim', 'brighten', 'scene'];
      const isIoTCommand = iotKeywords.some(keyword => command.toLowerCase().includes(keyword));

      if (isIoTCommand) {
        // Forward to IoT controller
        try {
          const iotResult = await axios.post('http://localhost:3010/tools/processVoiceCommand', {
            command,
            speaker
          }, { timeout: 10000 });

          return {
            success: true,
            type: 'iot_command',
            agent_lee_result: parseResult.response,
            iot_result: iotResult.data,
            speaker,
            timestamp: new Date()
          };

        } catch (iotError) {
          // If IoT controller is not available, return Agent Lee result
          return {
            success: true,
            type: 'agent_lee_only',
            result: parseResult.response,
            iot_error: 'IoT controller not available',
            speaker,
            timestamp: new Date()
          };
        }
      }

      return {
        success: true,
        type: 'agent_lee_command',
        result: parseResult.response,
        speaker,
        timestamp: new Date()
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Voice command processing failed'
      };
    }
  }

  private async getSpeakerStats(speaker: string): Promise<any> {
    try {
      const result = await this.makeAgentLeeRequest(`/api/speakers/${speaker}/stats`);

      return {
        success: result.success,
        speaker,
        stats: result.data,
        error: result.success ? undefined : result.error
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get speaker stats'
      };
    }
  }

  private async listAgentLeeTools(): Promise<any> {
    try {
      const result = await this.makeAgentLeeRequest('/api/tools');

      return {
        success: result.success,
        tools: result.data?.tools || [],
        count: result.data?.tools?.length || 0,
        error: result.success ? undefined : result.error
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to list tools'
      };
    }
  }

  private async generateBadge(speaker: string, badgeType?: string): Promise<any> {
    try {
      const result = await this.makeAgentLeeRequest(`/api/speakers/${speaker}/badges`);

      return {
        success: result.success,
        speaker,
        badges: result.data,
        error: result.success ? undefined : result.error
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to generate badge'
      };
    }
  }

  private async generateCertificate(speaker: string, certificateType?: string, achievements?: string[]): Promise<any> {
    try {
      const payload: any = {};
      if (certificateType) payload.type = certificateType;
      if (achievements) payload.achievements = achievements;

      const result = await this.makeAgentLeeRequest(`/api/speakers/${speaker}/certificate`, 'POST', payload);

      return {
        success: result.success,
        speaker,
        certificate: result.data,
        error: result.success ? undefined : result.error
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to generate certificate'
      };
    }
  }

  private async healthCheck(): Promise<any> {
    try {
      const result = await this.makeAgentLeeRequest('/health');

      return {
        success: result.success,
        status: result.data?.status || 'unknown',
        service: result.data?.service || 'Agent Lee Backend',
        timestamp: new Date(),
        error: result.success ? undefined : result.error
      };

    } catch (error) {
      return {
        success: false,
        status: 'error',
        error: error instanceof Error ? error.message : 'Health check failed',
        timestamp: new Date()
      };
    }
  }

  protected override async getResourceContent(resource: any): Promise<any> {
    try {
      switch (resource.uri) {
        case 'agentlee://sessions':
          const sessionsResult = await this.makeAgentLeeRequest('/api/sessions');
          return sessionsResult.success ? sessionsResult.data : [];

        case 'agentlee://memory':
          const memoryResult = await this.makeAgentLeeRequest('/api/memory/search');
          return memoryResult.success ? memoryResult.data : [];

        case 'agentlee://leaderboard':
          const leaderboardResult = await this.makeAgentLeeRequest('/api/leaderboard');
          return leaderboardResult.success ? leaderboardResult.data : [];

        case 'agentlee://tools':
          const toolsResult = await this.makeAgentLeeRequest('/api/tools');
          return toolsResult.success ? toolsResult.data : [];

        case 'agentlee://system-status':
          const healthResult = await this.healthCheck();
          return {
            health: healthResult,
            backend_url: this.agentLeeConfig.baseUrl,
            mcp_bridge_status: 'operational',
            last_check: new Date()
          };

        default:
          return null;
      }
    } catch (error) {
      console.error(`❌ Failed to get resource content for ${resource.uri}:`, error);
      return null;
    }
  }

  // Enhanced method to integrate with IoT controller
  private async integrateWithIoTController(): Promise<void> {
    try {
      // Test connection to IoT controller
      const response = await axios.get('http://localhost:3010/health', { timeout: 5000 });
      if (response.status === 200) {
        console.log('🏠 Connected to IoT Controller');

        // Register IoT-specific tools in Agent Lee
        await this.registerIoTToolsWithAgentLee();
      }
    } catch (error) {
      console.warn('⚠️ IoT Controller not available, continuing without IoT integration');
    }
  }

  private async registerIoTToolsWithAgentLee(): Promise<void> {
    // This would register IoT capabilities with the main Agent Lee backend
    // so it knows about IoT commands and can route them appropriately
    try {
      const iotTools = [
        {
          name: 'control_lights',
          description: 'Control smart lights (on/off/dim/color)',
          category: 'iot'
        },
        {
          name: 'control_cameras',
          description: 'Control security cameras',
          category: 'iot'
        },
        {
          name: 'control_locks',
          description: 'Control smart locks',
          category: 'iot'
        },
        {
          name: 'activate_scene',
          description: 'Activate home automation scenes',
          category: 'iot'
        }
      ];

      // In a real implementation, you would register these with Agent Lee's tool registry
      console.log('🔧 IoT tools registered with Agent Lee backend');

    } catch (error) {
      console.error('❌ Failed to register IoT tools:', error);
    }
  }

  override async start(): Promise<void> {
    await super.start();

    // Initialize IoT integration
    await this.integrateWithIoTController();

    console.log('🌉 Agent Lee Bridge MCP Server started successfully');
  }
}

// Export the server class and start function
export { AgentLeeBridgeMCPServer };

export async function startAgentLeeBridgeServer(port: number = 3013): Promise<AgentLeeBridgeMCPServer> {
  const server = new AgentLeeBridgeMCPServer(port);
  await server.start();
  console.log(`🌉 Agent Lee Bridge MCP Server started on port ${port}`);
  return server;
}

// Start server if this file is run directly
if (require.main === module) {
  startAgentLeeBridgeServer().catch(console.error);
}
