import { MCPServer } from 'model-context-protocol';
import axios from 'axios';

export async function startServer(port = 3018, aiEndpoint: string) {
    const server = new MCPServer({ port });
    server.registerTool('buildSlides', {
        title: 'AI Slide Builder',
        description: 'Generate slide deck from content outline',
        parameters: {
            type: 'object',
            properties: {
                outline: { type: 'array', items: { type: 'string' } },
                title: { type: 'string' }
            },
            required: ['outline']
        },
        returns: {
            type: 'object',
            properties: {
                slides: { type: 'array', items: { type: 'object', properties: { text: { type: 'string' }, imageUrl: { type: 'string' } } } }
            }
        }
    }, async ({ title, outline }) => {
        const resp = await axios.post(aiEndpoint, { title, outline });
        return { slides: resp.data.slides };
    });
    await server.listen();
}