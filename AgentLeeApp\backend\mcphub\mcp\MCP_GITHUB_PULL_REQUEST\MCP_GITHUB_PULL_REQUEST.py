import requests

class GitHubPRMCP:
    def __init__(self, token: str):
        self.token = token
        self.headers = {
            "Authorization": f"token {self.token}",
            "Accept": "application/vnd.github.v3+json"
        }

    def create_pull_request(self, owner: str, repo: str, title: str, body: str, head: str, base: str):
        url = f"https://api.github.com/repos/{owner}/{repo}/pulls"
        payload = {
            "title": title,
            "body": body,
            "head": head,
            "base": base
        }
        response = requests.post(url, headers=self.headers, json=payload)
        response.raise_for_status()
        return response.json()

    def list_pull_requests(self, owner: str, repo: str, state: str = "open"):
        url = f"https://api.github.com/repos/{owner}/{repo}/pulls?state={state}"
        response = requests.get(url, headers=self.headers)
        response.raise_for_status()
        return response.json()
