"""
Unified IoT Command Dispatcher for Agent Lee
Central coordination system for complex IoT automation scenarios across all MCP services
"""

import asyncio
import json
import requests
import re
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class ServiceType(Enum):
    IOT_CONTROLLER = "iot_controller"
    PHONE_CONTROL = "phone_control"
    CHROME_CONTROL = "chrome_control"
    WEB_SEARCH = "web_search"
    HOME_ASSISTANT = "home_assistant"
    AGENT_LEE_BRIDGE = "agent_lee_bridge"
    MOBILE_MCP = "mobile_mcp"
    PLAYWRIGHT_MCP = "playwright_mcp"

@dataclass
class ServiceEndpoint:
    name: str
    url: str
    port: int
    health_path: str = "/health"
    
    @property
    def full_url(self) -> str:
        return f"{self.url}:{self.port}"

@dataclass
class WorkflowStep:
    service: ServiceType
    action: str
    params: Dict[str, Any]
    priority: int = 1
    depends_on: Optional[List[str]] = None
    timeout: int = 30
    retry_count: int = 3

@dataclass
class WorkflowResult:
    step_id: str
    service: ServiceType
    success: bool
    result: Any
    error: Optional[str] = None
    execution_time: float = 0.0

class UnifiedIoTDispatcher:
    """Central dispatcher for coordinating IoT automation workflows across all MCP services."""
    
    def __init__(self):
        self.services = {
            ServiceType.IOT_CONTROLLER: ServiceEndpoint("IoT Controller", "http://localhost", 3010),
            ServiceType.PHONE_CONTROL: ServiceEndpoint("Phone Control", "http://localhost", 3004),
            ServiceType.CHROME_CONTROL: ServiceEndpoint("Chrome Control", "http://localhost", 3005),
            ServiceType.WEB_SEARCH: ServiceEndpoint("Web Search", "http://localhost", 3006),
            ServiceType.HOME_ASSISTANT: ServiceEndpoint("Home Assistant", "http://localhost", 3011),
            ServiceType.AGENT_LEE_BRIDGE: ServiceEndpoint("Agent Lee Bridge", "http://localhost", 3013),
            ServiceType.MOBILE_MCP: ServiceEndpoint("Mobile MCP", "http://localhost", 9010),
            ServiceType.PLAYWRIGHT_MCP: ServiceEndpoint("Playwright MCP", "http://localhost", 3001),
        }
        
        self.workflow_patterns = self._initialize_workflow_patterns()
        self.active_workflows: Dict[str, List[WorkflowStep]] = {}
        self.workflow_results: Dict[str, List[WorkflowResult]] = {}
    
    def _initialize_workflow_patterns(self) -> Dict[str, List[WorkflowStep]]:
        """Initialize predefined workflow patterns for common IoT scenarios."""
        return {
            # Security Alert Workflow
            "security_alert": [
                WorkflowStep(
                    service=ServiceType.IOT_CONTROLLER,
                    action="get_security_status",
                    params={},
                    priority=1
                ),
                WorkflowStep(
                    service=ServiceType.CHROME_CONTROL,
                    action="open_security_cameras",
                    params={"cameras": ["front_door", "backyard"]},
                    priority=2
                ),
                WorkflowStep(
                    service=ServiceType.PHONE_CONTROL,
                    action="send_alert_sms",
                    params={"message": "Security alert detected", "contacts": ["emergency"]},
                    priority=3
                )
            ],
            
            # Leaving Home Workflow
            "leaving_home": [
                WorkflowStep(
                    service=ServiceType.IOT_CONTROLLER,
                    action="activate_scene",
                    params={"scene": "away"},
                    priority=1
                ),
                WorkflowStep(
                    service=ServiceType.PHONE_CONTROL,
                    action="send_confirmation",
                    params={"message": "House secured, away mode activated"},
                    priority=2
                )
            ],
            
            # Movie Night Workflow
            "movie_night": [
                WorkflowStep(
                    service=ServiceType.IOT_CONTROLLER,
                    action="activate_scene",
                    params={"scene": "movie_night"},
                    priority=1
                ),
                WorkflowStep(
                    service=ServiceType.CHROME_CONTROL,
                    action="open_streaming_service",
                    params={"service": "netflix"},
                    priority=2
                )
            ],
            
            # Device Troubleshooting Workflow
            "device_troubleshooting": [
                WorkflowStep(
                    service=ServiceType.IOT_CONTROLLER,
                    action="diagnose_device",
                    params={},
                    priority=1
                ),
                WorkflowStep(
                    service=ServiceType.WEB_SEARCH,
                    action="search_troubleshooting",
                    params={},
                    priority=2
                ),
                WorkflowStep(
                    service=ServiceType.PLAYWRIGHT_MCP,
                    action="open_device_interface",
                    params={},
                    priority=3
                )
            ]
        }
    
    async def dispatch_command(self, command: str, speaker: str = "unknown") -> Dict[str, Any]:
        """Main entry point for dispatching IoT commands."""
        try:
            # Parse command and determine workflow
            workflow_id = f"workflow_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{speaker}"
            workflow_steps = await self._parse_command_to_workflow(command, speaker)
            
            if not workflow_steps:
                return {"error": "Could not parse command into workflow", "command": command}
            
            # Execute workflow
            self.active_workflows[workflow_id] = workflow_steps
            results = await self._execute_workflow(workflow_id, workflow_steps)
            self.workflow_results[workflow_id] = results
            
            return {
                "success": True,
                "workflow_id": workflow_id,
                "command": command,
                "speaker": speaker,
                "steps_executed": len(results),
                "results": [self._serialize_result(r) for r in results],
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "command": command,
                "speaker": speaker,
                "timestamp": datetime.now().isoformat()
            }
    
    async def _parse_command_to_workflow(self, command: str, speaker: str) -> List[WorkflowStep]:
        """Parse natural language command into workflow steps."""
        command_lower = command.lower()
        
        # Check for predefined patterns
        for pattern_name, steps in self.workflow_patterns.items():
            if self._matches_pattern(command_lower, pattern_name):
                return self._customize_workflow_steps(steps, command, speaker)
        
        # Parse complex commands
        if "and" in command_lower or "then" in command_lower:
            return await self._parse_complex_command(command, speaker)
        
        # Single service commands
        return await self._parse_single_command(command, speaker)
    
    def _matches_pattern(self, command: str, pattern_name: str) -> bool:
        """Check if command matches a predefined pattern."""
        pattern_keywords = {
            "security_alert": ["security", "alert", "motion", "intrusion", "break"],
            "leaving_home": ["leaving", "away", "lock", "secure", "goodbye"],
            "movie_night": ["movie", "film", "netflix", "entertainment", "dim lights"],
            "device_troubleshooting": ["troubleshoot", "fix", "problem", "not working", "broken"]
        }
        
        keywords = pattern_keywords.get(pattern_name, [])
        return any(keyword in command for keyword in keywords)
    
    def _customize_workflow_steps(self, steps: List[WorkflowStep], command: str, speaker: str) -> List[WorkflowStep]:
        """Customize workflow steps based on specific command and speaker."""
        customized_steps = []
        
        for step in steps:
            new_params = step.params.copy()
            new_params.update({
                "original_command": command,
                "speaker": speaker,
                "timestamp": datetime.now().isoformat()
            })
            
            customized_step = WorkflowStep(
                service=step.service,
                action=step.action,
                params=new_params,
                priority=step.priority,
                depends_on=step.depends_on,
                timeout=step.timeout,
                retry_count=step.retry_count
            )
            customized_steps.append(customized_step)
        
        return customized_steps
    
    async def _parse_complex_command(self, command: str, speaker: str) -> List[WorkflowStep]:
        """Parse complex commands with multiple actions."""
        steps = []
        priority = 1
        
        # Split by conjunctions
        parts = re.split(r'\s+(?:and|then)\s+', command.lower())
        
        for part in parts:
            part = part.strip()
            if not part:
                continue
            
            # Determine service and action for each part
            service, action, params = await self._parse_command_part(part, speaker)
            if service:
                steps.append(WorkflowStep(
                    service=service,
                    action=action,
                    params=params,
                    priority=priority
                ))
                priority += 1
        
        return steps
    
    async def _parse_single_command(self, command: str, speaker: str) -> List[WorkflowStep]:
        """Parse single service commands."""
        service, action, params = await self._parse_command_part(command.lower(), speaker)
        
        if service:
            return [WorkflowStep(
                service=service,
                action=action,
                params=params,
                priority=1
            )]
        
        return []
    
    async def _parse_command_part(self, command_part: str, speaker: str) -> Tuple[Optional[ServiceType], str, Dict[str, Any]]:
        """Parse individual command part to determine service, action, and parameters."""
        params = {"command": command_part, "speaker": speaker}
        
        # Phone control patterns
        if any(keyword in command_part for keyword in ["call", "phone", "sms", "text", "message"]):
            return ServiceType.PHONE_CONTROL, "voice_command", params
        
        # Browser control patterns
        if any(keyword in command_part for keyword in ["open", "browser", "website", "show", "display"]):
            return ServiceType.CHROME_CONTROL, "voice_command", params
        
        # Search patterns
        if any(keyword in command_part for keyword in ["search", "find", "lookup", "google"]):
            return ServiceType.WEB_SEARCH, "iot_search", params
        
        # IoT device control patterns (default)
        return ServiceType.IOT_CONTROLLER, "process_voice_command", params
    
    async def _execute_workflow(self, workflow_id: str, steps: List[WorkflowStep]) -> List[WorkflowResult]:
        """Execute workflow steps in priority order."""
        results = []

        # Sort steps by priority
        sorted_steps = sorted(steps, key=lambda x: x.priority)

        for i, step in enumerate(sorted_steps):
            step_id = f"{workflow_id}_step_{i+1}"

            # Check dependencies
            if step.depends_on:
                dependency_met = all(
                    any(r.step_id.endswith(dep) and r.success for r in results)
                    for dep in step.depends_on
                )
                if not dependency_met:
                    results.append(WorkflowResult(
                        step_id=step_id,
                        service=step.service,
                        success=False,
                        result=None,
                        error="Dependencies not met"
                    ))
                    continue

            # Execute step with retries
            result = await self._execute_step_with_retry(step_id, step)
            results.append(result)

            # Stop on critical failure
            if not result.success and step.priority == 1:
                break

        return results

    async def _execute_step_with_retry(self, step_id: str, step: WorkflowStep) -> WorkflowResult:
        """Execute a single workflow step with retry logic."""
        start_time = datetime.now()

        for attempt in range(step.retry_count):
            try:
                result = await self._execute_single_step(step)
                execution_time = (datetime.now() - start_time).total_seconds()

                return WorkflowResult(
                    step_id=step_id,
                    service=step.service,
                    success=True,
                    result=result,
                    execution_time=execution_time
                )

            except Exception as e:
                if attempt == step.retry_count - 1:  # Last attempt
                    execution_time = (datetime.now() - start_time).total_seconds()
                    return WorkflowResult(
                        step_id=step_id,
                        service=step.service,
                        success=False,
                        result=None,
                        error=str(e),
                        execution_time=execution_time
                    )

                # Wait before retry
                await asyncio.sleep(1)

    async def _execute_single_step(self, step: WorkflowStep) -> Any:
        """Execute a single workflow step."""
        service_endpoint = self.services[step.service]

        # Build request URL
        url = f"{service_endpoint.full_url}/api/{step.action}"

        # Make request
        response = requests.post(
            url,
            json=step.params,
            timeout=step.timeout
        )

        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Service request failed: {response.status_code} - {response.text}")

    def get_workflow_status(self, workflow_id: str) -> Dict[str, Any]:
        """Get status of a specific workflow."""
        if workflow_id not in self.active_workflows:
            return {"error": "Workflow not found"}

        steps = self.active_workflows[workflow_id]
        results = self.workflow_results.get(workflow_id, [])

        return {
            "workflow_id": workflow_id,
            "total_steps": len(steps),
            "completed_steps": len(results),
            "success_rate": len([r for r in results if r.success]) / len(results) if results else 0,
            "status": "completed" if len(results) == len(steps) else "running",
            "results": [self._serialize_result(r) for r in results]
        }

    def list_active_workflows(self) -> Dict[str, Any]:
        """List all active workflows."""
        return {
            "active_workflows": list(self.active_workflows.keys()),
            "completed_workflows": list(self.workflow_results.keys()),
            "total_workflows": len(self.active_workflows)
        }

    async def check_service_health(self) -> Dict[str, Any]:
        """Check health of all MCP services."""
        health_status = {}

        for service_type, endpoint in self.services.items():
            try:
                response = requests.get(
                    f"{endpoint.full_url}{endpoint.health_path}",
                    timeout=5
                )
                health_status[service_type.value] = {
                    "status": "healthy" if response.status_code == 200 else "unhealthy",
                    "url": endpoint.full_url,
                    "response_time": response.elapsed.total_seconds()
                }
            except Exception as e:
                health_status[service_type.value] = {
                    "status": "offline",
                    "url": endpoint.full_url,
                    "error": str(e)
                }

        return {
            "timestamp": datetime.now().isoformat(),
            "services": health_status,
            "healthy_services": len([s for s in health_status.values() if s["status"] == "healthy"]),
            "total_services": len(health_status)
        }

    def _serialize_result(self, result: WorkflowResult) -> Dict[str, Any]:
        """Serialize workflow result for JSON response."""
        return {
            "step_id": result.step_id,
            "service": result.service.value,
            "success": result.success,
            "result": result.result,
            "error": result.error,
            "execution_time": result.execution_time
        }

# Global dispatcher instance
dispatcher = UnifiedIoTDispatcher()

# Flask app for REST API
from flask import Flask, request, jsonify

app = Flask(__name__)

@app.route('/api/dispatch', methods=['POST'])
def dispatch_command():
    """Dispatch IoT command through unified dispatcher."""
    data = request.get_json()
    command = data.get('command', '')
    speaker = data.get('speaker', 'unknown')

    if not command:
        return jsonify({"error": "Command is required"}), 400

    # Run async dispatch
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        result = loop.run_until_complete(dispatcher.dispatch_command(command, speaker))
        return jsonify(result)
    finally:
        loop.close()

@app.route('/api/workflow/<workflow_id>/status', methods=['GET'])
def get_workflow_status(workflow_id):
    """Get workflow status."""
    return jsonify(dispatcher.get_workflow_status(workflow_id))

@app.route('/api/workflows', methods=['GET'])
def list_workflows():
    """List all workflows."""
    return jsonify(dispatcher.list_active_workflows())

@app.route('/api/health', methods=['GET'])
def check_health():
    """Check system health."""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        result = loop.run_until_complete(dispatcher.check_service_health())
        return jsonify(result)
    finally:
        loop.close()

@app.route('/health')
def health_check():
    """Health check endpoint."""
    return jsonify({
        "status": "healthy",
        "service": "unified-iot-dispatcher",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=3020, debug=True)
