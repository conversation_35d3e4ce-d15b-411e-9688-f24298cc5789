from playwright.sync_api import sync_playwright

class PlaywrightMCP:
    def __init__(self, headless=True):
        self.headless = headless

    def take_screenshot(self, url: str, path: str = "playwright_screenshot.png"):
        try:
            with sync_playwright() as p:
                browser = p.chromium.launch(headless=self.headless)
                page = browser.new_page()
                page.goto(url)
                page.screenshot(path=path)
                browser.close()
                return {"status": "captured", "path": path}
        except Exception as e:
            return {"error": str(e)}
