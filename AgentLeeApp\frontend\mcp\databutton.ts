import { MCPServer } from 'model-context-protocol';
import express from 'express';

export async function startServer(port = 3010, httpPort = 4000) {
    const app = express();
    app.use(express.json());
    let lastPayload: any = null;
    app.post('/button', (req, res) => {
        lastPayload = req.body;
        res.json({ received: true });
    });
    app.listen(httpPort, () => console.log(`HTTP endpoint listening on ${httpPort}`));
    const server = new MCPServer({ port });
    server.registerTool('getLastButton', {
        title: 'Get Last Button Payload',
        description: 'Retrieve last JSON sent to button endpoint',
        parameters: { type: 'object', properties: {} },
        returns: { type: 'object', properties: { payload: { type: 'object' } } }
    }, async () => ({ payload: lastPayload }));
    await server.listen();
}