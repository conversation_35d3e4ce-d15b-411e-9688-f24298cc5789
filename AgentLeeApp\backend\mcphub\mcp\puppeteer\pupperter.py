import subprocess
import os
import uuid

class PuppeteerMCP:
    def __init__(self, script_dir="puppeteer_scripts"):
        os.makedirs(script_dir, exist_ok=True)
        self.script_dir = script_dir

    def run_script(self, js_code: str):
        script_id = str(uuid.uuid4())
        script_path = os.path.join(self.script_dir, f"{script_id}.js")

        with open(script_path, "w") as f:
            f.write(js_code)

        try:
            result = subprocess.run(["node", script_path], capture_output=True, text=True, timeout=10)
            return {
                "stdout": result.stdout.strip(),
                "stderr": result.stderr.strip(),
                "exit_code": result.returncode
            }
        except subprocess.TimeoutExpired:
            return {"error": "Script timed out"}
        except Exception as e:
            return {"error": str(e)}
        finally:
            os.remove(script_path)
