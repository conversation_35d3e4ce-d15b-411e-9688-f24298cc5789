from fastapi import FastAPI, HTTPException, Body
from webhook_system import WebhookSystemMCP

mcp = WebhookSystemMCP()
app = FastAPI()

@app.post("/webhook")
def webhook_receive(event_type: str = Body(...), payload: dict = Body(...)):
    try:
        return mcp.register_event(event_type, payload)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/webhook/history")
def list_all_events():
    try:
        return mcp.list_events()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
