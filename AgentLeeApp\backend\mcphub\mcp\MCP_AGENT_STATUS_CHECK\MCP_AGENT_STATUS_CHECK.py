import time
import random

class AgentStatusCheckMCP:
    def __init__(self):
        self.status = {
            "Agent Lee": "online",
            "Sales Agent": "online",
            "Showcase Agent": "idle",
            "Resource Agent": "offline"
        }

    def ping_agent(self, name: str):
        if name not in self.status:
            return {"agent": name, "status": "unknown"}
        simulated_latency = round(random.uniform(0.1, 0.9), 2)
        time.sleep(simulated_latency)
        return {"agent": name, "status": self.status[name], "ping": f"{simulated_latency}s"}

    def list_all(self):
        return self.status
