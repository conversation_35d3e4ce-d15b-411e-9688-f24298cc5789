import React from 'react';

interface HeaderProps {
  avatar: string;
}

export const Header: React.FC<HeaderProps> = ({ avatar }) => (
  <header className="flex items-center space-x-4 py-2">
    <img
      src={avatar}
      alt="Agent Lee Avatar"
      className="w-12 h-12 rounded-full border-2 border-blue-500 shadow-lg"
    />
    <h1 className="text-3xl font-bold text-blue-400 tracking-tight">Agent <PERSON></h1>
    <span className="ml-2 px-3 py-1 bg-blue-800 text-xs rounded-full text-white font-semibold">MCP Dashboard</span>
  </header>
);
