from fastapi import FastAP<PERSON>, HTTPException
from mcp_base import MCPBase

mcp = MCPBase(name="MCP-BASE")
app = FastAPI()

@app.get("/mcp/ping")
def ping():
    try:
        return mcp.ping()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/mcp/identify")
def identify():
    try:
        return mcp.identify()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/mcp/reset")
def reset():
    try:
        return mcp.reset()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
