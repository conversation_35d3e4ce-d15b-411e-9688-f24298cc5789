from fastapi import FastAP<PERSON>, HTTPException, Query, Body
from notion import NotionMCP

# Replace with your Notion secret key
mcp = NotionMCP(api_key="YOUR_NOTION_API_KEY")

app = FastAPI()

@app.get("/notion/query")
def query_database(database_id: str = Query(...)):
    try:
        return mcp.query_database(database_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/notion/create")
def create_page(database_id: str = Body(...), title: str = Body(...)):
    try:
        return mcp.create_page(database_id, title)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
