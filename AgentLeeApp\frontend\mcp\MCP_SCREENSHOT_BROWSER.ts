
/**
 * Enhanced MCP Screenshot Browser with proper error handling and lifecycle management
 */
import { MCPServer, MCPTool, createToolSchema, ToolPropertyTypes } from '../mcp-base';
import puppeteer, { <PERSON><PERSON><PERSON> } from 'puppeteer';

interface ScreenshotServerOptions {
  port?: number;
  maxConcurrentBrowsers?: number;
  browserTimeout?: number;
  enableHealthCheck?: boolean;
}

interface ScreenshotParams {
  url: string;
  width?: number;
  height?: number;
  fullPage?: boolean;
  waitForSelector?: string;
  delay?: number;
}

class ScreenshotMCPServer extends MCPServer {
  private activeBrowsers: Set<Browser> = new Set();
  private isShuttingDown = false;
  private options: Required<ScreenshotServerOptions>;

  constructor(options: ScreenshotServerOptions = {}) {
    const serverOptions = {
      port: options.port || 3002,
      maxConcurrentBrowsers: options.maxConcurrentBrowsers || 3,
      browserTimeout: options.browserTimeout || 60000,
      enableHealthCheck: options.enableHealthCheck || true
    };

    super({
      name: 'Screenshot MCP Server',
      version: '1.0.0',
      port: serverOptions.port
    });

    this.options = serverOptions;
    this.setupTools();
    this.setupGracefulShutdown();
  }

  private setupTools() {
    const screenshotTool: MCPTool = {
      name: 'screenshot',
      description: 'Capture screenshot of a URL or local file with enhanced error handling',
      inputSchema: createToolSchema({
        url: ToolPropertyTypes.string('URL to capture screenshot from'),
        width: ToolPropertyTypes.number('Viewport width', 100, 4000),
        height: ToolPropertyTypes.number('Viewport height', 100, 4000),
        fullPage: ToolPropertyTypes.boolean('Capture full page', false),
        waitForSelector: ToolPropertyTypes.string('CSS selector to wait for before taking screenshot', ''),
        delay: ToolPropertyTypes.number('Delay in ms before taking screenshot', 0, 10000)
      }, ['url']),
      handler: async (params: ScreenshotParams) => this.handleScreenshot(params)
    };

    this.addTool(screenshotTool);
  }

  private async handleScreenshot({ url, width = 1280, height = 720, fullPage = false, waitForSelector, delay = 0 }: ScreenshotParams) {
    if (this.isShuttingDown) {
      throw new Error('Server is shutting down, cannot process new requests');
    }

    if (this.activeBrowsers.size >= this.options.maxConcurrentBrowsers) {
      throw new Error(`Maximum concurrent browsers (${this.options.maxConcurrentBrowsers}) reached`);
    }

    let browser: Browser | null = null;
    const startTime = Date.now();

    try {
      // Validate URL
      new URL(url);

      // Launch browser with enhanced options
      browser = await puppeteer.launch({
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ],
        headless: true,
        timeout: 30000
      });

      this.activeBrowsers.add(browser);
      this.emit('browserLaunched', { browserId: browser.process()?.pid });

      const page = await browser.newPage();

      // Set viewport with validation
      await page.setViewport({
        width: Math.max(100, Math.min(4000, width)),
        height: Math.max(100, Math.min(4000, height))
      });

      // Enhanced navigation with better error handling
      await page.goto(url, {
        waitUntil: 'networkidle2',
        timeout: this.options.browserTimeout
      });

      // Wait for specific selector if provided
      if (waitForSelector) {
        await page.waitForSelector(waitForSelector, { timeout: 10000 });
      }

      // Add delay if specified
      if (delay > 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }

      // Take screenshot with error handling
      const buffer = await page.screenshot({
        fullPage,
        type: 'png'
      });

      const image_base64 = buffer.toString();
      const metadata = {
        url,
        timestamp: new Date().toISOString(),
        dimensions: { width, height, fullPage },
        success: true,
        processingTime: Date.now() - startTime
      };

      this.emit('screenshotSuccess', metadata);
      return { image_base64, metadata };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorMetadata = {
        url,
        timestamp: new Date().toISOString(),
        success: false,
        error: errorMessage,
        processingTime: Date.now() - startTime
      };

      this.emit('screenshotError', errorMetadata);
      throw new Error(`Screenshot failed: ${errorMessage}`);
    } finally {
      if (browser) {
        try {
          await browser.close();
          this.activeBrowsers.delete(browser);
          this.emit('browserClosed', { browserId: browser.process()?.pid });
        } catch (closeError) {
          console.error('Error closing browser:', closeError);
        }
      }
    }
  }



  private setupGracefulShutdown() {
    const shutdown = async (signal: string) => {
      console.log(`Received ${signal}, starting graceful shutdown...`);
      this.isShuttingDown = true;

      // Close all active browsers
      const browserClosePromises = Array.from(this.activeBrowsers).map(async (browser) => {
        try {
          await browser.close();
        } catch (error) {
          console.error('Error closing browser during shutdown:', error);
        }
      });

      await Promise.all(browserClosePromises);
      this.activeBrowsers.clear();

      // Close server
      if (this.server) {
        await this.server.close();
      }

      console.log('Graceful shutdown completed');
      process.exit(0);
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGUSR2', () => shutdown('SIGUSR2')); // For nodemon
  }

  override async start(port?: number): Promise<void> {
    try {
      await super.start(port);
      console.log(`🖼️  Screenshot MCP server listening on port ${this.options.port}`);
      console.log(`📊 Max concurrent browsers: ${this.options.maxConcurrentBrowsers}`);
      console.log(`⏱️  Browser timeout: ${this.options.browserTimeout}ms`);
      this.emit('serverStarted', { port: this.options.port });
    } catch (error) {
      console.error('Failed to start screenshot MCP server:', error);
      throw error;
    }
  }

  override async stop(): Promise<void> {
    this.isShuttingDown = true;

    // Close all browsers
    for (const browser of this.activeBrowsers) {
      try {
        await browser.close();
      } catch (error) {
        console.error('Error closing browser:', error);
      }
    }
    this.activeBrowsers.clear();

    // Close server
    await super.stop();

    this.emit('serverStopped');
  }
}

export async function startServer(options: ScreenshotServerOptions = {}) {
  const screenshotServer = new ScreenshotMCPServer(options);
  await screenshotServer.start();
  return screenshotServer;
}

// For backwards compatibility
export { ScreenshotMCPServer };
