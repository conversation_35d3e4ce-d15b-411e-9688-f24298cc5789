from fastapi import Fast<PERSON><PERSON>, HTTPException, Body
from MCP_IMAGE_GENERATOR import ImageGeneratorMC<PERSON>

# Replace with your OpenAI API key
mcp = ImageGeneratorMCP(api_key="YOUR_OPENAI_API_KEY")

app = FastAPI()

@app.post("/image/generate")
def generate(prompt: str = Body(...), size: str = "512x512"):
    try:
        return mcp.generate_image(prompt, size)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
