import requests

class DuckDuckGoMCP:
    def __init__(self):
        self.api_url = "https://api.duckduckgo.com/"

    def instant_answer(self, query: str):
        params = {
            "q": query,
            "format": "json",
            "no_html": 1,
            "skip_disambig": 1
        }
        response = requests.get(self.api_url, params=params)
        response.raise_for_status()
        return response.json()
