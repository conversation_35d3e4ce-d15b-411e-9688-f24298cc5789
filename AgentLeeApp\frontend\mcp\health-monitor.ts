/**
 * MCP Health Monitoring System
 * Provides comprehensive health checks, metrics collection, and alerting
 */

import { EventEmitter } from 'events';
import axios from 'axios';
import fs from 'fs/promises';
import path from 'path';

interface HealthMetrics {
  timestamp: Date;
  service: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  responseTime: number;
  cpuUsage?: number;
  memoryUsage?: number;
  activeConnections?: number;
  errorRate?: number;
  customMetrics?: Record<string, any>;
}

interface AlertRule {
  name: string;
  condition: (metrics: HealthMetrics) => boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';
  cooldown: number; // minutes
  lastTriggered?: Date;
}

interface MonitorConfig {
  checkInterval: number; // milliseconds
  retentionPeriod: number; // hours
  alertThresholds: {
    responseTime: number;
    errorRate: number;
    memoryUsage: number;
  };
  enablePersistence: boolean;
  persistencePath: string;
}

class MCPHealthMonitor extends EventEmitter {
  private services: Map<string, string> = new Map(); // name -> url
  private metrics: Map<string, HealthMetrics[]> = new Map();
  private alertRules: AlertRule[] = [];
  private config: MonitorConfig;
  private monitorTimer?: NodeJS.Timeout;
  private cleanupTimer?: NodeJS.Timeout;

  constructor(config: Partial<MonitorConfig> = {}) {
    super();
    
    this.config = {
      checkInterval: config.checkInterval || 30000, // 30 seconds
      retentionPeriod: config.retentionPeriod || 24, // 24 hours
      alertThresholds: {
        responseTime: config.alertThresholds?.responseTime || 5000, // 5 seconds
        errorRate: config.alertThresholds?.errorRate || 0.1, // 10%
        memoryUsage: config.alertThresholds?.memoryUsage || 0.8, // 80%
        ...config.alertThresholds
      },
      enablePersistence: config.enablePersistence !== false,
      persistencePath: config.persistencePath || './health-data'
    };

    this.setupDefaultAlertRules();
    this.loadServices();
  }

  private setupDefaultAlertRules() {
    this.alertRules = [
      {
        name: 'High Response Time',
        condition: (metrics) => metrics.responseTime > this.config.alertThresholds.responseTime,
        severity: 'medium',
        cooldown: 5
      },
      {
        name: 'Service Unhealthy',
        condition: (metrics) => metrics.status === 'unhealthy',
        severity: 'high',
        cooldown: 1
      },
      {
        name: 'High Memory Usage',
        condition: (metrics) => (metrics.memoryUsage || 0) > this.config.alertThresholds.memoryUsage,
        severity: 'medium',
        cooldown: 10
      },
      {
        name: 'High Error Rate',
        condition: (metrics) => (metrics.errorRate || 0) > this.config.alertThresholds.errorRate,
        severity: 'high',
        cooldown: 5
      }
    ];
  }

  private async loadServices() {
    try {
      const settingsPath = path.join(__dirname, 'mcp_settings.json');
      const settings = JSON.parse(await fs.readFile(settingsPath, 'utf-8'));
      
      if (settings.mcpServers) {
        Object.entries(settings.mcpServers).forEach(([name, url]) => {
          this.services.set(name, url as string);
          this.metrics.set(name, []);
        });
      }

      console.log(`Loaded ${this.services.size} services for monitoring`);
    } catch (error) {
      console.error('Failed to load services:', error);
    }
  }

  async addService(name: string, url: string): Promise<void> {
    this.services.set(name, url);
    this.metrics.set(name, []);
    console.log(`Added service to monitoring: ${name} -> ${url}`);
  }

  async removeService(name: string): Promise<void> {
    this.services.delete(name);
    this.metrics.delete(name);
    console.log(`Removed service from monitoring: ${name}`);
  }

  private async checkServiceHealth(name: string, url: string): Promise<HealthMetrics> {
    const startTime = Date.now();
    let status: 'healthy' | 'unhealthy' | 'degraded' = 'unhealthy';
    let customMetrics: Record<string, any> = {};

    try {
      const response = await axios.get(`${url}/health`, {
        timeout: 10000,
        validateStatus: (status) => status < 500 // Accept 4xx as degraded, not unhealthy
      });

      const responseTime = Date.now() - startTime;

      if (response.status === 200) {
        status = 'healthy';
        
        // Extract additional metrics from health endpoint
        if (response.data) {
          customMetrics = {
            uptime: response.data.uptime,
            activeBrowsers: response.data.activeBrowsers,
            memory: response.data.memory,
            version: response.data.version
          };
        }
      } else if (response.status < 500) {
        status = 'degraded';
      }

      return {
        timestamp: new Date(),
        service: name,
        status,
        responseTime,
        customMetrics
      };

    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      return {
        timestamp: new Date(),
        service: name,
        status: 'unhealthy',
        responseTime,
        customMetrics: {
          error: error instanceof Error ? error.message : String(error)
        }
      };
    }
  }

  private async performHealthChecks(): Promise<void> {
    const checkPromises = Array.from(this.services.entries()).map(
      async ([name, url]) => {
        try {
          const metrics = await this.checkServiceHealth(name, url);
          this.recordMetrics(name, metrics);
          this.checkAlerts(metrics);
          return metrics;
        } catch (error) {
          console.error(`Health check failed for ${name}:`, error);
          return null;
        }
      }
    );

    const results = await Promise.allSettled(checkPromises);
    const successfulChecks = results.filter(r => r.status === 'fulfilled' && r.value).length;
    
    this.emit('healthCheckComplete', {
      timestamp: new Date(),
      totalServices: this.services.size,
      successfulChecks,
      failedChecks: this.services.size - successfulChecks
    });
  }

  private recordMetrics(serviceName: string, metrics: HealthMetrics): void {
    const serviceMetrics = this.metrics.get(serviceName) || [];
    serviceMetrics.push(metrics);

    // Keep only recent metrics based on retention period
    const cutoffTime = new Date(Date.now() - this.config.retentionPeriod * 60 * 60 * 1000);
    const filteredMetrics = serviceMetrics.filter(m => m.timestamp > cutoffTime);
    
    this.metrics.set(serviceName, filteredMetrics);

    // Persist metrics if enabled
    if (this.config.enablePersistence) {
      this.persistMetrics(serviceName, metrics);
    }

    this.emit('metricsRecorded', { service: serviceName, metrics });
  }

  private async persistMetrics(serviceName: string, metrics: HealthMetrics): Promise<void> {
    try {
      await fs.mkdir(this.config.persistencePath, { recursive: true });
      
      const date = metrics.timestamp.toISOString().split('T')[0];
      const filename = `${serviceName}-${date}.json`;
      const filepath = path.join(this.config.persistencePath, filename);
      
      let existingData: HealthMetrics[] = [];
      try {
        const content = await fs.readFile(filepath, 'utf-8');
        existingData = JSON.parse(content);
      } catch {
        // File doesn't exist, start with empty array
      }
      
      existingData.push(metrics);
      await fs.writeFile(filepath, JSON.stringify(existingData, null, 2));
    } catch (error) {
      console.error('Failed to persist metrics:', error);
    }
  }

  private checkAlerts(metrics: HealthMetrics): void {
    const now = new Date();
    
    this.alertRules.forEach(rule => {
      // Check cooldown
      if (rule.lastTriggered) {
        const timeSinceLastAlert = now.getTime() - rule.lastTriggered.getTime();
        const cooldownMs = rule.cooldown * 60 * 1000;
        
        if (timeSinceLastAlert < cooldownMs) {
          return; // Still in cooldown
        }
      }

      // Check condition
      if (rule.condition(metrics)) {
        rule.lastTriggered = now;
        
        const alert = {
          rule: rule.name,
          service: metrics.service,
          severity: rule.severity,
          timestamp: now,
          metrics,
          message: this.generateAlertMessage(rule, metrics)
        };

        this.emit('alert', alert);
        console.warn(`🚨 ALERT [${rule.severity.toUpperCase()}]: ${alert.message}`);
      }
    });
  }

  private generateAlertMessage(rule: AlertRule, metrics: HealthMetrics): string {
    switch (rule.name) {
      case 'High Response Time':
        return `${metrics.service} response time is ${metrics.responseTime}ms (threshold: ${this.config.alertThresholds.responseTime}ms)`;
      case 'Service Unhealthy':
        return `${metrics.service} is unhealthy`;
      case 'High Memory Usage':
        return `${metrics.service} memory usage is ${(metrics.memoryUsage || 0) * 100}% (threshold: ${this.config.alertThresholds.memoryUsage * 100}%)`;
      case 'High Error Rate':
        return `${metrics.service} error rate is ${(metrics.errorRate || 0) * 100}% (threshold: ${this.config.alertThresholds.errorRate * 100}%)`;
      default:
        return `${rule.name} triggered for ${metrics.service}`;
    }
  }

  // Public API methods
  async start(): Promise<void> {
    console.log('🔍 Starting MCP Health Monitor...');
    
    // Perform initial health check
    await this.performHealthChecks();
    
    // Start periodic monitoring
    this.monitorTimer = setInterval(() => {
      this.performHealthChecks();
    }, this.config.checkInterval);

    // Start cleanup timer
    this.cleanupTimer = setInterval(() => {
      this.cleanupOldMetrics();
    }, 60 * 60 * 1000); // Cleanup every hour

    console.log(`📊 Health monitoring started (interval: ${this.config.checkInterval}ms)`);
    this.emit('started');
  }

  async stop(): Promise<void> {
    if (this.monitorTimer) {
      clearInterval(this.monitorTimer);
    }
    
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    console.log('🔍 Health monitoring stopped');
    this.emit('stopped');
  }

  private cleanupOldMetrics(): void {
    const cutoffTime = new Date(Date.now() - this.config.retentionPeriod * 60 * 60 * 1000);
    
    this.metrics.forEach((serviceMetrics, serviceName) => {
      const filteredMetrics = serviceMetrics.filter(m => m.timestamp > cutoffTime);
      this.metrics.set(serviceName, filteredMetrics);
    });
  }

  // Query methods
  getServiceMetrics(serviceName: string, hours: number = 1): HealthMetrics[] {
    const serviceMetrics = this.metrics.get(serviceName) || [];
    const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
    
    return serviceMetrics.filter(m => m.timestamp > cutoffTime);
  }

  getServiceStatus(serviceName: string): 'healthy' | 'unhealthy' | 'degraded' | 'unknown' {
    const serviceMetrics = this.metrics.get(serviceName) || [];
    if (serviceMetrics.length === 0) return 'unknown';
    
    const latestMetrics = serviceMetrics[serviceMetrics.length - 1];
    return latestMetrics.status;
  }

  getAllServicesStatus(): Record<string, 'healthy' | 'unhealthy' | 'degraded' | 'unknown'> {
    const status: Record<string, 'healthy' | 'unhealthy' | 'degraded' | 'unknown'> = {};
    
    this.services.forEach((_, serviceName) => {
      status[serviceName] = this.getServiceStatus(serviceName);
    });
    
    return status;
  }

  getHealthSummary(): {
    totalServices: number;
    healthy: number;
    unhealthy: number;
    degraded: number;
    unknown: number;
  } {
    const statuses = this.getAllServicesStatus();
    const summary = {
      totalServices: this.services.size,
      healthy: 0,
      unhealthy: 0,
      degraded: 0,
      unknown: 0
    };

    Object.values(statuses).forEach(status => {
      summary[status]++;
    });

    return summary;
  }

  addAlertRule(rule: AlertRule): void {
    this.alertRules.push(rule);
  }

  removeAlertRule(name: string): boolean {
    const index = this.alertRules.findIndex(rule => rule.name === name);
    if (index !== -1) {
      this.alertRules.splice(index, 1);
      return true;
    }
    return false;
  }
}

export { MCPHealthMonitor };
export type { HealthMetrics, AlertRule, MonitorConfig };

// Start monitor if this file is run directly
if (require.main === module) {
  const monitor = new MCPHealthMonitor();
  
  monitor.on('alert', (alert) => {
    console.log('🚨 Alert:', alert);
  });
  
  monitor.on('healthCheckComplete', (summary) => {
    console.log('✅ Health check complete:', summary);
  });
  
  monitor.start().catch(console.error);
  
  // Graceful shutdown
  process.on('SIGTERM', () => monitor.stop());
  process.on('SIGINT', () => monitor.stop());
}
